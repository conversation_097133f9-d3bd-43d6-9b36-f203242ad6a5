#include "EmptyPanel.h"

#include <QFile>
#include <QTextStream>
#include <QStatusBar>
#include <QTimer>
#include <QLabel>
// #include <AuthDialog.h>

#include <QVBoxLayout>
#include <QPushButton>
#include <QSlider>

EmptyPanel::EmptyPanel(QWidget *parent) : QFrame(parent)
{
  setFrameShape(QFrame::StyledPanel);
  setFrameShadow(QFrame::Raised);
  setLineWidth(3); // 设置边框线宽为3像素
  setStyleSheet("background-color: #e2e2e2; padding: 10px;");

  QLabel *label = new QLabel("你好", this);
  label->setStyleSheet("font-size: 16px; color: #333333;");

  QVBoxLayout *layout = new QVBoxLayout(this);
  layout->addWidget(label);
  layout->setAlignment(label, Qt::AlignCenter);
}

EmptyPanel::~EmptyPanel()
{
  // do something when destroy
}