#include "TablePanel.h"
#include <QHeaderView>
#include <QMessageBox>
#include <QDateTime>

TablePanel::TablePanel(QWidget *parent) : QFrame(parent)
{
    setupUI();
    setupTable();
    setupButtons();
    setupSampleData();
}

TablePanel::~TablePanel()
{
    // Qt会自动清理子组件
}

void TablePanel::setupUI()
{
    // 设置Frame样式
    setFrameShape(QFrame::StyledPanel);
    setFrameShadow(QFrame::Raised);
    setLineWidth(2);
    setStyleSheet("QFrame { background-color: #f8f9fa; border: 1px solid #dee2e6; }");

    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);

    // 创建标题标签
    QLabel *titleLabel = new QLabel("交易数据表格", this);
    titleLabel->setStyleSheet("QLabel { font-size: 16px; font-weight: bold; color: black; }");
    titleLabel->setAlignment(Qt::AlignCenter);
    m_mainLayout->addWidget(titleLabel);
}

void TablePanel::setupTable()
{
    // 创建表格
    m_tableWidget = new QTableWidget(this);
    
    // 设置列数和列标题
    m_tableWidget->setColumnCount(6);
    QStringList headers;
    headers << "交易ID" << "产品名称" << "交易类型" << "数量" << "价格" << "时间";
    m_tableWidget->setHorizontalHeaderLabels(headers);
    
    // 设置表格属性
    m_tableWidget->setAlternatingRowColors(true);
    m_tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    m_tableWidget->setSortingEnabled(true);
    
    // 设置列宽
    m_tableWidget->horizontalHeader()->setStretchLastSection(true);
    m_tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
    m_tableWidget->setColumnWidth(0, 80);   // 交易ID
    m_tableWidget->setColumnWidth(1, 120);  // 产品名称
    m_tableWidget->setColumnWidth(2, 80);   // 交易类型
    m_tableWidget->setColumnWidth(3, 80);   // 数量
    m_tableWidget->setColumnWidth(4, 100);  // 价格
    
    // 设置表格样式
    m_tableWidget->setStyleSheet(
        "QTableWidget {"
        "    gridline-color: #dee2e6;"
        "    background-color: white;"
        "    alternate-background-color: #f8f9fa;"
        "    color: black;"
        "}"
        "QTableWidget::item {"
        "    padding: 5px;"
        "    border: none;"
        "    color: black;"
        "}"
        "QTableWidget::item:selected {"
        "    background-color: #007bff;"
        "    color: white;"
        "}"
        "QHeaderView::section {"
        "    background-color: #e9ecef;"
        "    padding: 8px;"
        "    border: 1px solid #dee2e6;"
        "    font-weight: bold;"
        "    color: black;"
        "}"
    );
    
    m_mainLayout->addWidget(m_tableWidget);
    
    // 连接信号
    connect(m_tableWidget, &QTableWidget::cellChanged, this, &TablePanel::onCellChanged);
    connect(m_tableWidget, &QTableWidget::itemSelectionChanged, this, &TablePanel::onSelectionChanged);
}

void TablePanel::setupButtons()
{
    // 创建按钮布局
    m_buttonLayout = new QHBoxLayout();
    
    // 创建按钮
    m_addRowButton = new QPushButton("添加行", this);
    m_removeRowButton = new QPushButton("删除选中行", this);
    m_clearButton = new QPushButton("清空表格", this);
    
    // 设置按钮样式
    QString buttonStyle = 
        "QPushButton {"
        "    background-color: #007bff;"
        "    color: white;"
        "    border: none;"
        "    padding: 8px 16px;"
        "    border-radius: 4px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #0056b3;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #004085;"
        "}";
    
    m_addRowButton->setStyleSheet(buttonStyle);
    m_removeRowButton->setStyleSheet(buttonStyle.replace("#007bff", "#dc3545").replace("#0056b3", "#c82333").replace("#004085", "#a02622"));
    m_clearButton->setStyleSheet(buttonStyle.replace("#007bff", "#6c757d").replace("#0056b3", "#5a6268").replace("#004085", "#495057"));
    
    // 添加按钮到布局
    m_buttonLayout->addWidget(m_addRowButton);
    m_buttonLayout->addWidget(m_removeRowButton);
    m_buttonLayout->addWidget(m_clearButton);
    m_buttonLayout->addStretch(); // 添加弹性空间
    
    // 创建状态标签
    m_statusLabel = new QLabel("就绪", this);
    m_statusLabel->setStyleSheet("QLabel { color: black; font-style: italic; }");
    m_buttonLayout->addWidget(m_statusLabel);
    
    m_mainLayout->addLayout(m_buttonLayout);
    
    // 连接按钮信号
    connect(m_addRowButton, &QPushButton::clicked, this, &TablePanel::onAddRowClicked);
    connect(m_removeRowButton, &QPushButton::clicked, this, &TablePanel::onRemoveRowClicked);
    connect(m_clearButton, &QPushButton::clicked, this, &TablePanel::onClearTableClicked);
    
    // 初始状态下禁用删除按钮
    m_removeRowButton->setEnabled(false);
}

void TablePanel::setupSampleData()
{
    // 添加一些示例数据
    QStringList sampleData1;
    sampleData1 << "T001" << "铜期货" << "买入" << "100" << "65,500.00" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    addRow(sampleData1);
    
    QStringList sampleData2;
    sampleData2 << "T002" << "铝期货" << "卖出" << "200" << "18,200.00" << QDateTime::currentDateTime().addSecs(-3600).toString("yyyy-MM-dd hh:mm:ss");
    addRow(sampleData2);
    
    QStringList sampleData3;
    sampleData3 << "T003" << "锌期货" << "买入" << "150" << "25,800.00" << QDateTime::currentDateTime().addSecs(-7200).toString("yyyy-MM-dd hh:mm:ss");
    addRow(sampleData3);
    
    m_statusLabel->setText(QString("已加载 %1 条记录").arg(m_tableWidget->rowCount()));
}

void TablePanel::addRow(const QStringList &rowData)
{
    int row = m_tableWidget->rowCount();
    m_tableWidget->insertRow(row);
    
    for (int col = 0; col < rowData.size() && col < m_tableWidget->columnCount(); ++col) {
        QTableWidgetItem *item = new QTableWidgetItem(rowData[col]);
        m_tableWidget->setItem(row, col, item);
    }
    
    m_statusLabel->setText(QString("总计 %1 条记录").arg(m_tableWidget->rowCount()));
}

void TablePanel::removeSelectedRow()
{
    int currentRow = m_tableWidget->currentRow();
    if (currentRow >= 0) {
        m_tableWidget->removeRow(currentRow);
        m_statusLabel->setText(QString("已删除行，总计 %1 条记录").arg(m_tableWidget->rowCount()));
    }
}

void TablePanel::clearTable()
{
    m_tableWidget->setRowCount(0);
    m_statusLabel->setText("表格已清空");
}

void TablePanel::onAddRowClicked()
{
    QStringList newRowData;
    QString newId = QString("T%1").arg(m_tableWidget->rowCount() + 1, 3, 10, QChar('0'));
    newRowData << newId << "新产品" << "买入" << "0" << "0.00" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    addRow(newRowData);
}

void TablePanel::onRemoveRowClicked()
{
    int ret = QMessageBox::question(this, "确认删除", "确定要删除选中的行吗？", 
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        removeSelectedRow();
    }
}

void TablePanel::onClearTableClicked()
{
    if (m_tableWidget->rowCount() == 0) {
        QMessageBox::information(this, "提示", "表格已经是空的！");
        return;
    }
    
    int ret = QMessageBox::question(this, "确认清空", "确定要清空整个表格吗？", 
                                   QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        clearTable();
    }
}

void TablePanel::onCellChanged(int row, int column)
{
    Q_UNUSED(row)
    Q_UNUSED(column)
    m_statusLabel->setText("数据已修改");
}

void TablePanel::onSelectionChanged()
{
    bool hasSelection = m_tableWidget->currentRow() >= 0;
    m_removeRowButton->setEnabled(hasSelection);
}
