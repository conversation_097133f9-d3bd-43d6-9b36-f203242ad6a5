#ifndef TABLEPANEL_H
#define TABLEPANEL_H

#include <QFrame>
#include <QTableWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QHeaderView>

class TablePanel : public QFrame
{
    Q_OBJECT

public:
    explicit TablePanel(QWidget *parent = nullptr);
    ~TablePanel();

    // 表格操作方法
    void addRow(const QStringList &rowData);
    void removeSelectedRow();
    void clearTable();
    void setupSampleData();

private slots:
    void onAddRowClicked();
    void onRemoveRowClicked();
    void onClearTableClicked();
    void onCellChanged(int row, int column);
    void onSelectionChanged();

private:
    void setupUI();
    void setupTable();
    void setupButtons();

    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_buttonLayout;
    QTableWidget *m_tableWidget;
    
    // 按钮
    QPushButton *m_addRowButton;
    QPushButton *m_removeRowButton;
    QPushButton *m_clearButton;
    
    // 状态标签
    QLabel *m_statusLabel;
};

#endif // TABLEPANEL_H
