#include "Console.h"
#include "ConfigDialog.h"

#include <QFile>
#include <QTextStream>
#include <QStatusBar>
#include <QTimer>
#include <QLabel>
#include <QGridLayout>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QScreen>
#include <QRect>
#include <QUrl>
#include <QSettings>
#include <QApplication>
// #include <AuthDialog.h>

#include <QVBoxLayout>
#include <QPushButton>
#include <QSlider>

#include <DefaultLayout.h>
#include <EmptyPanel.h>
// #include <WebView2Widget.h>
Console::Console(QWidget *parent) : QMainWindow(parent)
{
  // QFile file(":/style/ThemeDark.qss");
  // testWidget = new TestWidget();
  // Temporarily commented out style loading for cross-platform compatibility
  // if (file.open(QFile::ReadOnly | QFile::Text))
  // {
  //   QTextStream stream(&file);
  //   this->setStyleSheet(stream.readAll());
  // }

  // 设置菜单栏
  setupMenuBar();

  // ��ȡ״̬��
  QStatusBar *statusBar = this->statusBar();
  QLabel *statusLabel = new QLabel("Ready", this);
  statusBar->addWidget(statusLabel);

  // 创建一个中心部件
  DefaultLayout *centralWidget = new DefaultLayout(this);
  // QWidget *centralWidget = new QWidget(this);
  // QVBoxLayout *layout = new QVBoxLayout(centralWidget);
  // WebView2Widget removed for cross-platform compatibility

  // connect(openAction, &QAction::triggered, this, &Console::loadUrl);


  // centralWidget->setNorthLeft(new EmptyPanel(centralWidget));
  // centralWidget->setNorthRight(new EmptyPanel(centralWidget));
  centralWidget->setCenter(new EmptyPanel(centralWidget));
  // centralWidget->setWest(new EmptyPanel(centralWidget));
  // centralWidget->setEast(new EmptyPanel(centralWidget));
  // centralWidget->setSouthWest(new EmptyPanel(centralWidget));
  // centralWidget->setSouthCenter(new EmptyPanel(centralWidget));
  // centralWidget->setSouthEast(new EmptyPanel(centralWidget));

  // layout->addWidget(testPanel);

  this->setCentralWidget(centralWidget);
  this->setWindowTitle("象屿股份场外衍生品交易系统");
  this->showMaximized();

  // 应用设置
  applySettings();

  /* QTimer* timer = new QTimer(this);
   connect(testWidget, &TestWidget::onMessage, this, [=](const QString& msg) {
       statusLabel->setText(QString("status: %1").arg(msg));
       });
   timer->start(20);*/
  // AuthDialog dlg;
  // dlg.exec();
}
void Console::setupMenuBar()
{
  QMenuBar *menuBar = this->menuBar();

  // 文件菜单
  QMenu *fileMenu = menuBar->addMenu("文件(&F)");
  QAction *newAction = new QAction("新建(&N)", this);
  QAction *openAction = new QAction("打开(&O)", this);
  fileMenu->addAction(newAction);
  fileMenu->addAction(openAction);
  fileMenu->addSeparator();

  exitAction = new QAction("退出(&X)", this);
  exitAction->setShortcut(QKeySequence::Quit);
  fileMenu->addAction(exitAction);
  connect(exitAction, &QAction::triggered, this, &Console::onExitTriggered);

  // 工具菜单
  QMenu *toolsMenu = menuBar->addMenu("工具(&T)");
  configAction = new QAction("配置(&C)...", this);
  configAction->setShortcut(QKeySequence("Ctrl+,"));
  toolsMenu->addAction(configAction);
  connect(configAction, &QAction::triggered, this, &Console::showConfigDialog);

  toolsMenu->addSeparator();
  webViewAction = new QAction("Web视图(&W)...", this);
  webViewAction->setShortcut(QKeySequence("Ctrl+W"));
  toolsMenu->addAction(webViewAction);
  connect(webViewAction, &QAction::triggered, this, &Console::showWebView);

  // 帮助菜单
  QMenu *helpMenu = menuBar->addMenu("帮助(&H)");
  QAction *aboutAction = new QAction("关于(&A)", this);
  helpMenu->addAction(aboutAction);

  // connect(openAction, &QAction::triggered, this, &Console::loadUrl);
}

void Console::showConfigDialog()
{
  ConfigDialog dialog(this);
  if (dialog.exec() == QDialog::Accepted) {
    // 配置已保存，重新应用设置
    applySettings();
  }
}

void Console::onExitTriggered()
{
  QApplication::quit();
}

void Console::applySettings()
{
  QSettings settings;

  // 应用窗口标题
  QString appName = settings.value("general/appName", "象屿股份场外衍生品交易系统").toString();
  setWindowTitle(appName);

  // 应用透明度
  int opacity = settings.value("appearance/opacity", 100).toInt();
  setWindowOpacity(opacity / 100.0);

  // 应用工具栏和状态栏显示设置
  bool showStatusBar = settings.value("appearance/showStatusBar", true).toBool();
  statusBar()->setVisible(showStatusBar);

  // 应用字体设置
  QString fontFamily = settings.value("appearance/fontFamily", QApplication::font().family()).toString();
  int fontSize = settings.value("appearance/fontSize", QApplication::font().pointSize()).toInt();
  QFont appFont(fontFamily, fontSize);
  setFont(appFont);
}

void Console::showWebView()
{
  // 创建WebView对话框
  QDialog *webDialog = new QDialog(this);
  webDialog->setWindowTitle("Web视图");
  webDialog->setModal(false);
  webDialog->resize(1000, 700);

  QVBoxLayout *layout = new QVBoxLayout(webDialog);

  // 添加地址栏
  QHBoxLayout *addressLayout = new QHBoxLayout();
  QLineEdit *addressEdit = new QLineEdit();
  addressEdit->setText("https://www.baidu.com");
  QPushButton *goButton = new QPushButton("转到");
  QPushButton *reloadButton = new QPushButton("刷新");

  addressLayout->addWidget(new QLabel("地址:"));
  addressLayout->addWidget(addressEdit);
  addressLayout->addWidget(goButton);
  addressLayout->addWidget(reloadButton);

  layout->addLayout(addressLayout);

  // 添加WebView（这里先用占位符，因为需要检查WebEngine是否可用）
  QLabel *webPlaceholder = new QLabel("WebView功能需要Qt WebEngine支持\n请确保已安装Qt WebEngine模块");
  webPlaceholder->setAlignment(Qt::AlignCenter);
  webPlaceholder->setStyleSheet("QLabel { background-color: #f0f0f0; border: 1px solid #ccc; padding: 20px; }");
  layout->addWidget(webPlaceholder);

  // 添加状态栏
  QStatusBar *statusBar = new QStatusBar();
  statusBar->showMessage("准备就绪");
  layout->addWidget(statusBar);

  // 连接信号
  connect(goButton, &QPushButton::clicked, [=]() {
    QString url = addressEdit->text();
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      url = "https://" + url;
    }
    statusBar->showMessage("正在加载: " + url);
    // 这里应该加载URL到WebView
  });

  connect(reloadButton, &QPushButton::clicked, [=]() {
    statusBar->showMessage("正在刷新...");
    // 这里应该刷新WebView
  });

  webDialog->show();
}

void Console::onWebViewLoadFinished(bool success)
{
  if (success) {
    qDebug() << "WebView loaded successfully";
  } else {
    qDebug() << "WebView load failed";
  }
}

void Console::loadUrl()
{
  // 现在可以使用跨平台WebView
  showWebView();
}

Console::~Console() {}
