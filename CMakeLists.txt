cmake_minimum_required(VERSION 3.20)

project(OtcdClient LANGUAGES CXX)

# 开启本项目头文件引用
set(CMAKE_INCLUDE_CURRENT_DIR ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 跨平台Qt路径配置
if(APPLE)
    # macOS Qt路径 (通过Homebrew安装)
    set(CMAKE_PREFIX_PATH "/opt/homebrew/opt/qt@6/lib/cmake" "/usr/local/opt/qt@6/lib/cmake")
elseif(WIN32)
    # Windows Qt路径
    set(CMAKE_PREFIX_PATH "E:/Qt/6.8.1/msvc2022_64/lib/cmake")
endif()

include_directories(
        ${CMAKE_SOURCE_DIR}/src/console
        ${CMAKE_SOURCE_DIR}/src/module
        ${CMAKE_SOURCE_DIR}/src/util
)

add_executable("${PROJECT_NAME}"
        src/app/main.cpp
        src/console/Console.cpp
        src/console/ConfigDialog.cpp
        src/console/DefaultLayout.cpp
        src/module/EmptyPanel.cpp
        src/module/TablePanel.cpp
        src/util/CrossPlatformWebView.cpp
)

find_package(Qt6 REQUIRED COMPONENTS Widgets WebEngineWidgets)

target_link_libraries("${PROJECT_NAME}"
        Qt6::Widgets
        Qt6::WebEngineWidgets
)
