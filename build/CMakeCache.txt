# This is the CMakeCache file.
# For build in directory: /Users/<USER>/sy&xy/code/self/C++/otc/build
# It was generated by CMake: /opt/homebrew/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:STRING=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OtcdClient

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Path to a file.
CUPS_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include

//Path to a library.
CUPS_LIBRARIES:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libcups.tbd

//Path to a program.
MACDEPLOYQT_EXECUTABLE:FILEPATH=/opt/homebrew/opt/qt@6/bin/macdeployqt

//Include for the OpenGL GLU library
OPENGL_GLU_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/OpenGL.framework

//Include for OpenGL
OPENGL_INCLUDE_DIR:PATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/OpenGL.framework

//OpenGL GL library
OPENGL_gl_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/OpenGL.framework

//OpenGL GLU library
OPENGL_glu_LIBRARY:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/OpenGL.framework

//Value Computed by CMake
OtcdClient_BINARY_DIR:STATIC=/Users/<USER>/sy&xy/code/self/C++/otc/build

//Value Computed by CMake
OtcdClient_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
OtcdClient_SOURCE_DIR:STATIC=/Users/<USER>/sy&xy/code/self/C++/otc

//Additional directories where find(Qt6 ...) host Qt components
// are searched
QT_ADDITIONAL_HOST_PACKAGES_PREFIX_PATH:STRING=

//Additional directories where find(Qt6 ...) components are searched
QT_ADDITIONAL_PACKAGES_PREFIX_PATH:STRING=

//The directory containing a CMake configuration file for Qt6ConcurrentPrivate.
Qt6ConcurrentPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6ConcurrentPrivate

//The directory containing a CMake configuration file for Qt6Concurrent.
Qt6Concurrent_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Concurrent

//The directory containing a CMake configuration file for Qt6CorePrivate.
Qt6CorePrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6CorePrivate

//The directory containing a CMake configuration file for Qt6CoreTools.
Qt6CoreTools_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6CoreTools

//The directory containing a CMake configuration file for Qt6Core.
Qt6Core_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Core

//The directory containing a CMake configuration file for Qt6DBusPrivate.
Qt6DBusPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6DBusPrivate

//The directory containing a CMake configuration file for Qt6DBusTools.
Qt6DBusTools_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6DBusTools

//The directory containing a CMake configuration file for Qt6DBus.
Qt6DBus_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6DBus

//The directory containing a CMake configuration file for Qt6ExamplesAssetDownloaderPrivate.
Qt6ExamplesAssetDownloaderPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6ExamplesAssetDownloaderPrivate

//The directory containing a CMake configuration file for Qt6GuiPrivate.
Qt6GuiPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6GuiPrivate

//The directory containing a CMake configuration file for Qt6GuiTools.
Qt6GuiTools_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6GuiTools

//The directory containing a CMake configuration file for Qt6Gui.
Qt6Gui_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Gui

//The directory containing a CMake configuration file for Qt6NetworkPrivate.
Qt6NetworkPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6NetworkPrivate

//The directory containing a CMake configuration file for Qt6Network.
Qt6Network_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Network

//The directory containing a CMake configuration file for Qt6OpenGLPrivate.
Qt6OpenGLPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6OpenGLPrivate

//The directory containing a CMake configuration file for Qt6OpenGL.
Qt6OpenGL_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6OpenGL

//The directory containing a CMake configuration file for Qt6PositioningPrivate.
Qt6PositioningPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6PositioningPrivate

//The directory containing a CMake configuration file for Qt6Positioning.
Qt6Positioning_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Positioning

//The directory containing a CMake configuration file for Qt6PrintSupportPrivate.
Qt6PrintSupportPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6PrintSupportPrivate

//The directory containing a CMake configuration file for Qt6PrintSupport.
Qt6PrintSupport_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6PrintSupport

//The directory containing a CMake configuration file for Qt6QmlAssetDownloaderPrivate.
Qt6QmlAssetDownloaderPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlAssetDownloaderPrivate

//The directory containing a CMake configuration file for Qt6QmlAssetDownloader.
Qt6QmlAssetDownloader_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlAssetDownloader

//The directory containing a CMake configuration file for Qt6QmlCompilerPlusPrivateTools.
Qt6QmlCompilerPlusPrivateTools_DIR:PATH=Qt6QmlCompilerPlusPrivateTools_DIR-NOTFOUND

//The directory containing a CMake configuration file for Qt6QmlIntegrationPrivate.
Qt6QmlIntegrationPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlIntegrationPrivate

//The directory containing a CMake configuration file for Qt6QmlIntegration.
Qt6QmlIntegration_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlIntegration

//The directory containing a CMake configuration file for Qt6QmlMetaPrivate.
Qt6QmlMetaPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlMetaPrivate

//The directory containing a CMake configuration file for Qt6QmlMeta.
Qt6QmlMeta_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlMeta

//The directory containing a CMake configuration file for Qt6QmlModelsPrivate.
Qt6QmlModelsPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlModelsPrivate

//The directory containing a CMake configuration file for Qt6QmlModels.
Qt6QmlModels_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlModels

//The directory containing a CMake configuration file for Qt6QmlPrivate.
Qt6QmlPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlPrivate

//The directory containing a CMake configuration file for Qt6QmlTools.
Qt6QmlTools_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlTools

//The directory containing a CMake configuration file for Qt6QmlWorkerScriptPrivate.
Qt6QmlWorkerScriptPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlWorkerScriptPrivate

//The directory containing a CMake configuration file for Qt6QmlWorkerScript.
Qt6QmlWorkerScript_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QmlWorkerScript

//The directory containing a CMake configuration file for Qt6Qml.
Qt6Qml_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Qml

//The directory containing a CMake configuration file for Qt6QuickPrivate.
Qt6QuickPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QuickPrivate

//The directory containing a CMake configuration file for Qt6QuickTools.
Qt6QuickTools_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QuickTools

//The directory containing a CMake configuration file for Qt6QuickWidgetsPrivate.
Qt6QuickWidgetsPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QuickWidgetsPrivate

//The directory containing a CMake configuration file for Qt6QuickWidgets.
Qt6QuickWidgets_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6QuickWidgets

//The directory containing a CMake configuration file for Qt6Quick.
Qt6Quick_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Quick

//The directory containing a CMake configuration file for Qt6WebChannelPrivate.
Qt6WebChannelPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WebChannelPrivate

//The directory containing a CMake configuration file for Qt6WebChannel.
Qt6WebChannel_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WebChannel

//The directory containing a CMake configuration file for Qt6WebEngineCorePrivate.
Qt6WebEngineCorePrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WebEngineCorePrivate

//The directory containing a CMake configuration file for Qt6WebEngineCoreTools.
Qt6WebEngineCoreTools_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WebEngineCoreTools

//The directory containing a CMake configuration file for Qt6WebEngineCore.
Qt6WebEngineCore_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WebEngineCore

//The directory containing a CMake configuration file for Qt6WebEngineWidgetsPrivate.
Qt6WebEngineWidgetsPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WebEngineWidgetsPrivate

//The directory containing a CMake configuration file for Qt6WebEngineWidgets.
Qt6WebEngineWidgets_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WebEngineWidgets

//The directory containing a CMake configuration file for Qt6WidgetsPrivate.
Qt6WidgetsPrivate_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WidgetsPrivate

//The directory containing a CMake configuration file for Qt6WidgetsTools.
Qt6WidgetsTools_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6WidgetsTools

//The directory containing a CMake configuration file for Qt6Widgets.
Qt6Widgets_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Widgets

//The directory containing a CMake configuration file for Qt6.
Qt6_DIR:PATH=/opt/homebrew/opt/qt@6/lib/cmake/Qt6

//Path to a program.
Vulkan_GLSLANG_VALIDATOR_EXECUTABLE:FILEPATH=Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-NOTFOUND

//Path to a program.
Vulkan_GLSLC_EXECUTABLE:FILEPATH=Vulkan_GLSLC_EXECUTABLE-NOTFOUND

//Path to a file.
Vulkan_INCLUDE_DIR:PATH=Vulkan_INCLUDE_DIR-NOTFOUND

//Path to a library.
Vulkan_LIBRARY:FILEPATH=Vulkan_LIBRARY-NOTFOUND

//Path to a library.
WrapOpenGL_AGL:FILEPATH=/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/AGL.framework


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/sy&xy/code/self/C++/otc/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/homebrew/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/homebrew/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/homebrew/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/homebrew/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/sy&xy/code/self/C++/otc
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/homebrew/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUPS_INCLUDE_DIR
CUPS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUPS_LIBRARIES
CUPS_LIBRARIES-ADVANCED:INTERNAL=1
//Details about finding Cups
FIND_PACKAGE_MESSAGE_DETAILS_Cups:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libcups.tbd][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/include][v2.3.4()]
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/OpenGL.framework][/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/System/Library/Frameworks/OpenGL.framework][ ][v()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding WrapAtomic
FIND_PACKAGE_MESSAGE_DETAILS_WrapAtomic:INTERNAL=[1][v()]
//Details about finding WrapOpenGL
FIND_PACKAGE_MESSAGE_DETAILS_WrapOpenGL:INTERNAL=[ON][v()]
//Test HAVE_STDATOMIC
HAVE_STDATOMIC:INTERNAL=1
//ADVANCED property for variable: OPENGL_GLU_INCLUDE_DIR
OPENGL_GLU_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_INCLUDE_DIR
OPENGL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gl_LIBRARY
OPENGL_gl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//Qt feature: abstractbutton (from target Qt6::Widgets)
QT_FEATURE_abstractbutton:INTERNAL=ON
//Qt feature: abstractslider (from target Qt6::Widgets)
QT_FEATURE_abstractslider:INTERNAL=ON
//Qt feature: accessibility (from target Qt6::Gui)
QT_FEATURE_accessibility:INTERNAL=ON
//Qt feature: accessibility_atspi_bridge (from target Qt6::Gui)
QT_FEATURE_accessibility_atspi_bridge:INTERNAL=OFF
//Qt feature: action (from target Qt6::Gui)
QT_FEATURE_action:INTERNAL=ON
//Qt feature: aesni (from target Qt6::Core)
QT_FEATURE_aesni:INTERNAL=OFF
//Qt feature: android_style_assets (from target Qt6::Core)
QT_FEATURE_android_style_assets:INTERNAL=OFF
//Qt feature: animation (from target Qt6::Core)
QT_FEATURE_animation:INTERNAL=ON
//Qt feature: appstore_compliant (from target Qt6::Core)
QT_FEATURE_appstore_compliant:INTERNAL=OFF
//Qt feature: arm_crc32 (from target Qt6::Core)
QT_FEATURE_arm_crc32:INTERNAL=ON
//Qt feature: arm_crypto (from target Qt6::Core)
QT_FEATURE_arm_crypto:INTERNAL=ON
//Qt feature: arm_sve (from target Qt6::Core)
QT_FEATURE_arm_sve:INTERNAL=ON
//Qt feature: avx (from target Qt6::Core)
QT_FEATURE_avx:INTERNAL=OFF
//Qt feature: avx2 (from target Qt6::Core)
QT_FEATURE_avx2:INTERNAL=OFF
//Qt feature: avx512bw (from target Qt6::Core)
QT_FEATURE_avx512bw:INTERNAL=OFF
//Qt feature: avx512cd (from target Qt6::Core)
QT_FEATURE_avx512cd:INTERNAL=OFF
//Qt feature: avx512dq (from target Qt6::Core)
QT_FEATURE_avx512dq:INTERNAL=OFF
//Qt feature: avx512er (from target Qt6::Core)
QT_FEATURE_avx512er:INTERNAL=OFF
//Qt feature: avx512f (from target Qt6::Core)
QT_FEATURE_avx512f:INTERNAL=OFF
//Qt feature: avx512ifma (from target Qt6::Core)
QT_FEATURE_avx512ifma:INTERNAL=OFF
//Qt feature: avx512pf (from target Qt6::Core)
QT_FEATURE_avx512pf:INTERNAL=OFF
//Qt feature: avx512vbmi (from target Qt6::Core)
QT_FEATURE_avx512vbmi:INTERNAL=OFF
//Qt feature: avx512vbmi2 (from target Qt6::Core)
QT_FEATURE_avx512vbmi2:INTERNAL=OFF
//Qt feature: avx512vl (from target Qt6::Core)
QT_FEATURE_avx512vl:INTERNAL=OFF
//Qt feature: backtrace (from target Qt6::Core)
QT_FEATURE_backtrace:INTERNAL=ON
//Qt feature: broken_threadlocal_dtors (from target Qt6::Core)
QT_FEATURE_broken_threadlocal_dtors:INTERNAL=OFF
//Qt feature: brotli (from target Qt6::Network)
QT_FEATURE_brotli:INTERNAL=ON
//Qt feature: buttongroup (from target Qt6::Widgets)
QT_FEATURE_buttongroup:INTERNAL=ON
//Qt feature: calendarwidget (from target Qt6::Widgets)
QT_FEATURE_calendarwidget:INTERNAL=ON
//Qt feature: cborstreamreader (from target Qt6::Core)
QT_FEATURE_cborstreamreader:INTERNAL=ON
//Qt feature: cborstreamwriter (from target Qt6::Core)
QT_FEATURE_cborstreamwriter:INTERNAL=ON
//Qt feature: checkbox (from target Qt6::Widgets)
QT_FEATURE_checkbox:INTERNAL=ON
//Qt feature: clipboard (from target Qt6::Gui)
QT_FEATURE_clipboard:INTERNAL=ON
//Qt feature: clock_gettime (from target Qt6::Core)
QT_FEATURE_clock_gettime:INTERNAL=ON
//Qt feature: clock_monotonic (from target Qt6::Core)
QT_FEATURE_clock_monotonic:INTERNAL=OFF
//Qt feature: colordialog (from target Qt6::Widgets)
QT_FEATURE_colordialog:INTERNAL=ON
//Qt feature: colornames (from target Qt6::Gui)
QT_FEATURE_colornames:INTERNAL=ON
//Qt feature: columnview (from target Qt6::Widgets)
QT_FEATURE_columnview:INTERNAL=ON
//Qt feature: combobox (from target Qt6::Widgets)
QT_FEATURE_combobox:INTERNAL=ON
//Qt feature: commandlineparser (from target Qt6::Core)
QT_FEATURE_commandlineparser:INTERNAL=ON
//Qt feature: commandlinkbutton (from target Qt6::Widgets)
QT_FEATURE_commandlinkbutton:INTERNAL=ON
//Qt feature: completer (from target Qt6::Widgets)
QT_FEATURE_completer:INTERNAL=ON
//Qt feature: concatenatetablesproxymodel (from target Qt6::Core)
QT_FEATURE_concatenatetablesproxymodel:INTERNAL=ON
//Qt feature: concurrent (from target Qt6::Core)
QT_FEATURE_concurrent:INTERNAL=ON
//Qt feature: contextmenu (from target Qt6::Widgets)
QT_FEATURE_contextmenu:INTERNAL=ON
//Qt feature: cpp_winrt (from target Qt6::Core)
QT_FEATURE_cpp_winrt:INTERNAL=OFF
//Qt feature: cross_compile (from target Qt6::Core)
QT_FEATURE_cross_compile:INTERNAL=OFF
//Qt feature: cssparser (from target Qt6::Gui)
QT_FEATURE_cssparser:INTERNAL=ON
//Qt feature: ctf (from target Qt6::Core)
QT_FEATURE_ctf:INTERNAL=OFF
//Qt feature: cups (from target Qt6::PrintSupport)
QT_FEATURE_cups:INTERNAL=ON
//Qt feature: cupsjobwidget (from target Qt6::PrintSupport)
QT_FEATURE_cupsjobwidget:INTERNAL=ON
//Qt feature: cupspassworddialog (from target Qt6::PrintSupport)
QT_FEATURE_cupspassworddialog:INTERNAL=ON
//Qt feature: cursor (from target Qt6::Gui)
QT_FEATURE_cursor:INTERNAL=ON
//Qt feature: cxx11_future (from target Qt6::Core)
QT_FEATURE_cxx11_future:INTERNAL=ON
//Qt feature: cxx17_filesystem (from target Qt6::Core)
QT_FEATURE_cxx17_filesystem:INTERNAL=ON
//Qt feature: cxx20 (from target Qt6::Core)
QT_FEATURE_cxx20:INTERNAL=OFF
//Qt feature: cxx20_format (from target Qt6::Core)
QT_FEATURE_cxx20_format:INTERNAL=OFF
//Qt feature: cxx23_stacktrace (from target Qt6::Core)
QT_FEATURE_cxx23_stacktrace:INTERNAL=OFF
//Qt feature: cxx2a (from target Qt6::Core)
QT_FEATURE_cxx2a:INTERNAL=OFF
//Qt feature: cxx2b (from target Qt6::Core)
QT_FEATURE_cxx2b:INTERNAL=OFF
//Qt feature: datawidgetmapper (from target Qt6::Widgets)
QT_FEATURE_datawidgetmapper:INTERNAL=ON
//Qt feature: datestring (from target Qt6::Core)
QT_FEATURE_datestring:INTERNAL=ON
//Qt feature: datetimeedit (from target Qt6::Widgets)
QT_FEATURE_datetimeedit:INTERNAL=ON
//Qt feature: datetimeparser (from target Qt6::Core)
QT_FEATURE_datetimeparser:INTERNAL=ON
//Qt feature: dbus (from target Qt6::Core)
QT_FEATURE_dbus:INTERNAL=ON
//Qt feature: dbus_linked (from target Qt6::Core)
QT_FEATURE_dbus_linked:INTERNAL=ON
//Qt feature: debug (from target Qt6::Core)
QT_FEATURE_debug:INTERNAL=OFF
//Qt feature: debug_and_release (from target Qt6::Core)
QT_FEATURE_debug_and_release:INTERNAL=OFF
//Qt feature: desktopservices (from target Qt6::Gui)
QT_FEATURE_desktopservices:INTERNAL=ON
//Qt feature: developer_build (from target Qt6::Core)
QT_FEATURE_developer_build:INTERNAL=OFF
//Qt feature: dial (from target Qt6::Widgets)
QT_FEATURE_dial:INTERNAL=ON
//Qt feature: dialog (from target Qt6::Widgets)
QT_FEATURE_dialog:INTERNAL=ON
//Qt feature: dialogbuttonbox (from target Qt6::Widgets)
QT_FEATURE_dialogbuttonbox:INTERNAL=ON
//Qt feature: direct2d (from target Qt6::Gui)
QT_FEATURE_direct2d:INTERNAL=OFF
//Qt feature: direct2d1_1 (from target Qt6::Gui)
QT_FEATURE_direct2d1_1:INTERNAL=OFF
//Qt feature: directfb (from target Qt6::Gui)
QT_FEATURE_directfb:INTERNAL=OFF
//Qt feature: directwrite (from target Qt6::Gui)
QT_FEATURE_directwrite:INTERNAL=OFF
//Qt feature: directwrite3 (from target Qt6::Gui)
QT_FEATURE_directwrite3:INTERNAL=OFF
//Qt feature: directwritecolrv1 (from target Qt6::Gui)
QT_FEATURE_directwritecolrv1:INTERNAL=OFF
//Qt feature: dladdr (from target Qt6::Core)
QT_FEATURE_dladdr:INTERNAL=ON
//Qt feature: dlopen (from target Qt6::Core)
QT_FEATURE_dlopen:INTERNAL=ON
//Qt feature: dnslookup (from target Qt6::Network)
QT_FEATURE_dnslookup:INTERNAL=ON
//Qt feature: doc_snippets (from target Qt6::Core)
QT_FEATURE_doc_snippets:INTERNAL=OFF
//Qt feature: dockwidget (from target Qt6::Widgets)
QT_FEATURE_dockwidget:INTERNAL=ON
//Qt feature: doubleconversion (from target Qt6::Core)
QT_FEATURE_doubleconversion:INTERNAL=ON
//Qt feature: draganddrop (from target Qt6::Gui)
QT_FEATURE_draganddrop:INTERNAL=ON
//Qt feature: drm_atomic (from target Qt6::Gui)
QT_FEATURE_drm_atomic:INTERNAL=OFF
//Qt feature: dtls (from target Qt6::Network)
QT_FEATURE_dtls:INTERNAL=ON
//Qt feature: dynamicgl (from target Qt6::Gui)
QT_FEATURE_dynamicgl:INTERNAL=OFF
//Qt feature: easingcurve (from target Qt6::Core)
QT_FEATURE_easingcurve:INTERNAL=ON
//Qt feature: effects (from target Qt6::Widgets)
QT_FEATURE_effects:INTERNAL=ON
//Qt feature: egl (from target Qt6::Gui)
QT_FEATURE_egl:INTERNAL=OFF
//Qt feature: egl_x11 (from target Qt6::Gui)
QT_FEATURE_egl_x11:INTERNAL=OFF
//Qt feature: eglfs (from target Qt6::Gui)
QT_FEATURE_eglfs:INTERNAL=OFF
//Qt feature: eglfs_brcm (from target Qt6::Gui)
QT_FEATURE_eglfs_brcm:INTERNAL=OFF
//Qt feature: eglfs_egldevice (from target Qt6::Gui)
QT_FEATURE_eglfs_egldevice:INTERNAL=OFF
//Qt feature: eglfs_gbm (from target Qt6::Gui)
QT_FEATURE_eglfs_gbm:INTERNAL=OFF
//Qt feature: eglfs_mali (from target Qt6::Gui)
QT_FEATURE_eglfs_mali:INTERNAL=OFF
//Qt feature: eglfs_openwfd (from target Qt6::Gui)
QT_FEATURE_eglfs_openwfd:INTERNAL=OFF
//Qt feature: eglfs_rcar (from target Qt6::Gui)
QT_FEATURE_eglfs_rcar:INTERNAL=OFF
//Qt feature: eglfs_viv (from target Qt6::Gui)
QT_FEATURE_eglfs_viv:INTERNAL=OFF
//Qt feature: eglfs_viv_wl (from target Qt6::Gui)
QT_FEATURE_eglfs_viv_wl:INTERNAL=OFF
//Qt feature: eglfs_vsp2 (from target Qt6::Gui)
QT_FEATURE_eglfs_vsp2:INTERNAL=OFF
//Qt feature: eglfs_x11 (from target Qt6::Gui)
QT_FEATURE_eglfs_x11:INTERNAL=OFF
//Qt feature: elf_private_full_version (from target Qt6::Core)
QT_FEATURE_elf_private_full_version:INTERNAL=OFF
//Qt feature: emojisegmenter (from target Qt6::Gui)
QT_FEATURE_emojisegmenter:INTERNAL=ON
//Qt feature: errormessage (from target Qt6::Widgets)
QT_FEATURE_errormessage:INTERNAL=ON
//Qt feature: etw (from target Qt6::Core)
QT_FEATURE_etw:INTERNAL=OFF
//Qt feature: evdev (from target Qt6::Gui)
QT_FEATURE_evdev:INTERNAL=OFF
//Qt feature: f16c (from target Qt6::Core)
QT_FEATURE_f16c:INTERNAL=OFF
//Qt feature: filedialog (from target Qt6::Widgets)
QT_FEATURE_filedialog:INTERNAL=ON
//Qt feature: filesystemiterator (from target Qt6::Core)
QT_FEATURE_filesystemiterator:INTERNAL=ON
//Qt feature: filesystemmodel (from target Qt6::Gui)
QT_FEATURE_filesystemmodel:INTERNAL=ON
//Qt feature: filesystemwatcher (from target Qt6::Core)
QT_FEATURE_filesystemwatcher:INTERNAL=ON
//Qt feature: fontcombobox (from target Qt6::Widgets)
QT_FEATURE_fontcombobox:INTERNAL=ON
//Qt feature: fontconfig (from target Qt6::Gui)
QT_FEATURE_fontconfig:INTERNAL=OFF
//Qt feature: fontdialog (from target Qt6::Widgets)
QT_FEATURE_fontdialog:INTERNAL=ON
//Qt feature: force_asserts (from target Qt6::Core)
QT_FEATURE_force_asserts:INTERNAL=OFF
//Qt feature: force_bundled_libs (from target Qt6::Core)
QT_FEATURE_force_bundled_libs:INTERNAL=OFF
//Qt feature: force_system_libs (from target Qt6::Core)
QT_FEATURE_force_system_libs:INTERNAL=OFF
//Qt feature: forkfd_pidfd (from target Qt6::Core)
QT_FEATURE_forkfd_pidfd:INTERNAL=OFF
//Qt feature: formlayout (from target Qt6::Widgets)
QT_FEATURE_formlayout:INTERNAL=ON
//Qt feature: framework (from target Qt6::Core)
QT_FEATURE_framework:INTERNAL=ON
//Qt feature: freetype (from target Qt6::Gui)
QT_FEATURE_freetype:INTERNAL=ON
//Qt feature: fscompleter (from target Qt6::Widgets)
QT_FEATURE_fscompleter:INTERNAL=ON
//Qt feature: futimens (from target Qt6::Core)
QT_FEATURE_futimens:INTERNAL=ON
//Qt feature: future (from target Qt6::Core)
QT_FEATURE_future:INTERNAL=ON
//Qt feature: gc_binaries (from target Qt6::Core)
QT_FEATURE_gc_binaries:INTERNAL=OFF
//Qt feature: gestures (from target Qt6::Core)
QT_FEATURE_gestures:INTERNAL=ON
//Qt feature: getauxval (from target Qt6::Core)
QT_FEATURE_getauxval:INTERNAL=OFF
//Qt feature: getentropy (from target Qt6::Core)
QT_FEATURE_getentropy:INTERNAL=ON
//Qt feature: getifaddrs (from target Qt6::Network)
QT_FEATURE_getifaddrs:INTERNAL=ON
//Qt feature: gif (from target Qt6::Gui)
QT_FEATURE_gif:INTERNAL=ON
//Qt feature: glib (from target Qt6::Core)
QT_FEATURE_glib:INTERNAL=ON
//Qt feature: glibc_fortify_source (from target Qt6::Core)
QT_FEATURE_glibc_fortify_source:INTERNAL=OFF
//Qt feature: graphicseffect (from target Qt6::Widgets)
QT_FEATURE_graphicseffect:INTERNAL=ON
//Qt feature: graphicsframecapture (from target Qt6::Gui)
QT_FEATURE_graphicsframecapture:INTERNAL=ON
//Qt feature: graphicsview (from target Qt6::Widgets)
QT_FEATURE_graphicsview:INTERNAL=ON
//Qt feature: groupbox (from target Qt6::Widgets)
QT_FEATURE_groupbox:INTERNAL=ON
//Qt feature: gssapi (from target Qt6::Network)
QT_FEATURE_gssapi:INTERNAL=ON
//Qt feature: gtk3 (from target Qt6::Widgets)
QT_FEATURE_gtk3:INTERNAL=OFF
//Qt feature: gui (from target Qt6::Core)
QT_FEATURE_gui:INTERNAL=ON
//Qt feature: gypsy (from target Qt6::Positioning)
QT_FEATURE_gypsy:INTERNAL=OFF
//Qt feature: harfbuzz (from target Qt6::Gui)
QT_FEATURE_harfbuzz:INTERNAL=ON
//Qt feature: highdpiscaling (from target Qt6::Gui)
QT_FEATURE_highdpiscaling:INTERNAL=ON
//Qt feature: hijricalendar (from target Qt6::Core)
QT_FEATURE_hijricalendar:INTERNAL=ON
//Qt feature: http (from target Qt6::Network)
QT_FEATURE_http:INTERNAL=ON
//Qt feature: ico (from target Qt6::Gui)
QT_FEATURE_ico:INTERNAL=ON
//Qt feature: icu (from target Qt6::Core)
QT_FEATURE_icu:INTERNAL=ON
//Qt feature: identityproxymodel (from target Qt6::Core)
QT_FEATURE_identityproxymodel:INTERNAL=ON
//Qt feature: im (from target Qt6::Gui)
QT_FEATURE_im:INTERNAL=ON
//Qt feature: image_heuristic_mask (from target Qt6::Gui)
QT_FEATURE_image_heuristic_mask:INTERNAL=ON
//Qt feature: image_text (from target Qt6::Gui)
QT_FEATURE_image_text:INTERNAL=ON
//Qt feature: imageformat_bmp (from target Qt6::Gui)
QT_FEATURE_imageformat_bmp:INTERNAL=ON
//Qt feature: imageformat_jpeg (from target Qt6::Gui)
QT_FEATURE_imageformat_jpeg:INTERNAL=ON
//Qt feature: imageformat_png (from target Qt6::Gui)
QT_FEATURE_imageformat_png:INTERNAL=ON
//Qt feature: imageformat_ppm (from target Qt6::Gui)
QT_FEATURE_imageformat_ppm:INTERNAL=ON
//Qt feature: imageformat_xbm (from target Qt6::Gui)
QT_FEATURE_imageformat_xbm:INTERNAL=ON
//Qt feature: imageformat_xpm (from target Qt6::Gui)
QT_FEATURE_imageformat_xpm:INTERNAL=ON
//Qt feature: imageformatplugin (from target Qt6::Gui)
QT_FEATURE_imageformatplugin:INTERNAL=ON
//Qt feature: imageio_text_loading (from target Qt6::Gui)
QT_FEATURE_imageio_text_loading:INTERNAL=ON
//Qt feature: inotify (from target Qt6::Core)
QT_FEATURE_inotify:INTERNAL=OFF
//Qt feature: inputdialog (from target Qt6::Widgets)
QT_FEATURE_inputdialog:INTERNAL=ON
//Qt feature: integrityfb (from target Qt6::Gui)
QT_FEATURE_integrityfb:INTERNAL=OFF
//Qt feature: integrityhid (from target Qt6::Gui)
QT_FEATURE_integrityhid:INTERNAL=OFF
//Qt feature: intelcet (from target Qt6::Core)
QT_FEATURE_intelcet:INTERNAL=OFF
//Qt feature: ipv6ifname (from target Qt6::Network)
QT_FEATURE_ipv6ifname:INTERNAL=ON
//Qt feature: islamiccivilcalendar (from target Qt6::Core)
QT_FEATURE_islamiccivilcalendar:INTERNAL=ON
//Qt feature: itemmodel (from target Qt6::Core)
QT_FEATURE_itemmodel:INTERNAL=ON
//Qt feature: itemviews (from target Qt6::Widgets)
QT_FEATURE_itemviews:INTERNAL=ON
//Qt feature: jalalicalendar (from target Qt6::Core)
QT_FEATURE_jalalicalendar:INTERNAL=ON
//Qt feature: journald (from target Qt6::Core)
QT_FEATURE_journald:INTERNAL=OFF
//Qt feature: jpeg (from target Qt6::Gui)
QT_FEATURE_jpeg:INTERNAL=ON
//Qt feature: keysequenceedit (from target Qt6::Widgets)
QT_FEATURE_keysequenceedit:INTERNAL=ON
//Qt feature: kms (from target Qt6::Gui)
QT_FEATURE_kms:INTERNAL=OFF
//Qt feature: label (from target Qt6::Widgets)
QT_FEATURE_label:INTERNAL=ON
//Qt feature: largefile (from target Qt6::Core)
QT_FEATURE_largefile:INTERNAL=ON
//Qt feature: lasx (from target Qt6::Core)
QT_FEATURE_lasx:INTERNAL=OFF
//Qt feature: lcdnumber (from target Qt6::Widgets)
QT_FEATURE_lcdnumber:INTERNAL=ON
//Qt feature: libcpp_hardening (from target Qt6::Core)
QT_FEATURE_libcpp_hardening:INTERNAL=ON
//Qt feature: libinput (from target Qt6::Gui)
QT_FEATURE_libinput:INTERNAL=OFF
//Qt feature: libinput_axis_api (from target Qt6::Gui)
QT_FEATURE_libinput_axis_api:INTERNAL=OFF
//Qt feature: libinput_hires_wheel_support (from target Qt6::Gui)
QT_FEATURE_libinput_hires_wheel_support:INTERNAL=OFF
//Qt feature: libproxy (from target Qt6::Network)
QT_FEATURE_libproxy:INTERNAL=OFF
//Qt feature: library (from target Qt6::Core)
QT_FEATURE_library:INTERNAL=ON
//Qt feature: libresolv (from target Qt6::Network)
QT_FEATURE_libresolv:INTERNAL=ON
//Qt feature: libstdcpp_assertions (from target Qt6::Core)
QT_FEATURE_libstdcpp_assertions:INTERNAL=OFF
//Qt feature: libudev (from target Qt6::Core)
QT_FEATURE_libudev:INTERNAL=OFF
//Qt feature: lineedit (from target Qt6::Widgets)
QT_FEATURE_lineedit:INTERNAL=ON
//Qt feature: linkat (from target Qt6::Core)
QT_FEATURE_linkat:INTERNAL=OFF
//Qt feature: linux_netlink (from target Qt6::Network)
QT_FEATURE_linux_netlink:INTERNAL=OFF
//Qt feature: linuxfb (from target Qt6::Gui)
QT_FEATURE_linuxfb:INTERNAL=OFF
//Qt feature: listview (from target Qt6::Widgets)
QT_FEATURE_listview:INTERNAL=ON
//Qt feature: listwidget (from target Qt6::Widgets)
QT_FEATURE_listwidget:INTERNAL=ON
//Qt feature: localserver (from target Qt6::Network)
QT_FEATURE_localserver:INTERNAL=ON
//Qt feature: localtime_r (from target Qt6::Core)
QT_FEATURE_localtime_r:INTERNAL=ON
//Qt feature: localtime_s (from target Qt6::Core)
QT_FEATURE_localtime_s:INTERNAL=OFF
//Qt feature: lsx (from target Qt6::Core)
QT_FEATURE_lsx:INTERNAL=OFF
//Qt feature: lttng (from target Qt6::Core)
QT_FEATURE_lttng:INTERNAL=OFF
//Qt feature: mainwindow (from target Qt6::Widgets)
QT_FEATURE_mainwindow:INTERNAL=ON
//Qt feature: mdiarea (from target Qt6::Widgets)
QT_FEATURE_mdiarea:INTERNAL=ON
//Qt feature: memmem (from target Qt6::Core)
QT_FEATURE_memmem:INTERNAL=ON
//Qt feature: memrchr (from target Qt6::Core)
QT_FEATURE_memrchr:INTERNAL=OFF
//Qt feature: menu (from target Qt6::Widgets)
QT_FEATURE_menu:INTERNAL=ON
//Qt feature: menubar (from target Qt6::Widgets)
QT_FEATURE_menubar:INTERNAL=ON
//Qt feature: messagebox (from target Qt6::Widgets)
QT_FEATURE_messagebox:INTERNAL=ON
//Qt feature: metal (from target Qt6::Gui)
QT_FEATURE_metal:INTERNAL=ON
//Qt feature: mimetype (from target Qt6::Core)
QT_FEATURE_mimetype:INTERNAL=ON
//Qt feature: mimetype_database (from target Qt6::Core)
QT_FEATURE_mimetype_database:INTERNAL=ON
//Qt feature: mips_dsp (from target Qt6::Core)
QT_FEATURE_mips_dsp:INTERNAL=OFF
//Qt feature: mips_dspr2 (from target Qt6::Core)
QT_FEATURE_mips_dspr2:INTERNAL=OFF
//Qt feature: movie (from target Qt6::Gui)
QT_FEATURE_movie:INTERNAL=ON
//Qt feature: mtdev (from target Qt6::Gui)
QT_FEATURE_mtdev:INTERNAL=OFF
//Qt feature: multiprocess (from target Qt6::Gui)
QT_FEATURE_multiprocess:INTERNAL=ON
//Qt feature: neon (from target Qt6::Core)
QT_FEATURE_neon:INTERNAL=ON
//Qt feature: network (from target Qt6::Core)
QT_FEATURE_network:INTERNAL=ON
//Qt feature: networkdiskcache (from target Qt6::Network)
QT_FEATURE_networkdiskcache:INTERNAL=ON
//Qt feature: networkinterface (from target Qt6::Network)
QT_FEATURE_networkinterface:INTERNAL=ON
//Qt feature: networklistmanager (from target Qt6::Network)
QT_FEATURE_networklistmanager:INTERNAL=OFF
//Qt feature: networkproxy (from target Qt6::Network)
QT_FEATURE_networkproxy:INTERNAL=ON
//Qt feature: no_direct_extern_access (from target Qt6::Core)
QT_FEATURE_no_direct_extern_access:INTERNAL=OFF
//Qt feature: ocsp (from target Qt6::Network)
QT_FEATURE_ocsp:INTERNAL=ON
//Qt feature: opengl (from target Qt6::Gui)
QT_FEATURE_opengl:INTERNAL=ON
//Qt feature: opengles2 (from target Qt6::Gui)
QT_FEATURE_opengles2:INTERNAL=OFF
//Qt feature: opengles3 (from target Qt6::Gui)
QT_FEATURE_opengles3:INTERNAL=OFF
//Qt feature: opengles31 (from target Qt6::Gui)
QT_FEATURE_opengles31:INTERNAL=OFF
//Qt feature: opengles32 (from target Qt6::Gui)
QT_FEATURE_opengles32:INTERNAL=OFF
//Qt feature: openssl (from target Qt6::Core)
QT_FEATURE_openssl:INTERNAL=ON
//Qt feature: openssl_hash (from target Qt6::Core)
QT_FEATURE_openssl_hash:INTERNAL=OFF
//Qt feature: openssl_linked (from target Qt6::Core)
QT_FEATURE_openssl_linked:INTERNAL=OFF
//Qt feature: opensslv11 (from target Qt6::Core)
QT_FEATURE_opensslv11:INTERNAL=OFF
//Qt feature: opensslv30 (from target Qt6::Core)
QT_FEATURE_opensslv30:INTERNAL=ON
//Qt feature: openvg (from target Qt6::Gui)
QT_FEATURE_openvg:INTERNAL=OFF
//Qt feature: pcre2 (from target Qt6::Core)
QT_FEATURE_pcre2:INTERNAL=ON
//Qt feature: pdf (from target Qt6::Gui)
QT_FEATURE_pdf:INTERNAL=ON
//Qt feature: permissions (from target Qt6::Core)
QT_FEATURE_permissions:INTERNAL=ON
//Qt feature: picture (from target Qt6::Gui)
QT_FEATURE_picture:INTERNAL=ON
//Qt feature: pkg_config (from target Qt6::Core)
QT_FEATURE_pkg_config:INTERNAL=ON
//Qt feature: plugin_manifest (from target Qt6::Core)
QT_FEATURE_plugin_manifest:INTERNAL=ON
//Qt feature: png (from target Qt6::Gui)
QT_FEATURE_png:INTERNAL=ON
//Qt feature: poll_exit_on_error (from target Qt6::Core)
QT_FEATURE_poll_exit_on_error:INTERNAL=OFF
//Qt feature: poll_poll (from target Qt6::Core)
QT_FEATURE_poll_poll:INTERNAL=ON
//Qt feature: poll_pollts (from target Qt6::Core)
QT_FEATURE_poll_pollts:INTERNAL=OFF
//Qt feature: poll_ppoll (from target Qt6::Core)
QT_FEATURE_poll_ppoll:INTERNAL=OFF
//Qt feature: poll_select (from target Qt6::Core)
QT_FEATURE_poll_select:INTERNAL=OFF
//Qt feature: posix_fallocate (from target Qt6::Core)
QT_FEATURE_posix_fallocate:INTERNAL=OFF
//Qt feature: posix_sem (from target Qt6::Core)
QT_FEATURE_posix_sem:INTERNAL=ON
//Qt feature: posix_shm (from target Qt6::Core)
QT_FEATURE_posix_shm:INTERNAL=ON
//Qt feature: precompile_header (from target Qt6::Core)
QT_FEATURE_precompile_header:INTERNAL=ON
//Qt feature: printdialog (from target Qt6::PrintSupport)
QT_FEATURE_printdialog:INTERNAL=ON
//Qt feature: printer (from target Qt6::PrintSupport)
QT_FEATURE_printer:INTERNAL=ON
//Qt feature: printpreviewdialog (from target Qt6::PrintSupport)
QT_FEATURE_printpreviewdialog:INTERNAL=ON
//Qt feature: printpreviewwidget (from target Qt6::PrintSupport)
QT_FEATURE_printpreviewwidget:INTERNAL=ON
//Qt feature: printsupport (from target Qt6::Core)
QT_FEATURE_printsupport:INTERNAL=ON
//Qt feature: private_tests (from target Qt6::Core)
QT_FEATURE_private_tests:INTERNAL=OFF
//Qt feature: process (from target Qt6::Core)
QT_FEATURE_process:INTERNAL=ON
//Qt feature: processenvironment (from target Qt6::Core)
QT_FEATURE_processenvironment:INTERNAL=ON
//Qt feature: progressbar (from target Qt6::Widgets)
QT_FEATURE_progressbar:INTERNAL=ON
//Qt feature: progressdialog (from target Qt6::Widgets)
QT_FEATURE_progressdialog:INTERNAL=ON
//Qt feature: proxymodel (from target Qt6::Core)
QT_FEATURE_proxymodel:INTERNAL=ON
//Qt feature: pthread_clockjoin (from target Qt6::Core)
QT_FEATURE_pthread_clockjoin:INTERNAL=OFF
//Qt feature: pthread_condattr_setclock (from target Qt6::Core)
QT_FEATURE_pthread_condattr_setclock:INTERNAL=OFF
//Qt feature: pthread_timedjoin (from target Qt6::Core)
QT_FEATURE_pthread_timedjoin:INTERNAL=OFF
//Qt feature: publicsuffix_qt (from target Qt6::Network)
QT_FEATURE_publicsuffix_qt:INTERNAL=ON
//Qt feature: publicsuffix_system (from target Qt6::Network)
QT_FEATURE_publicsuffix_system:INTERNAL=OFF
//Qt feature: pushbutton (from target Qt6::Widgets)
QT_FEATURE_pushbutton:INTERNAL=ON
//Qt feature: qml_animation (from target Qt6::Qml)
QT_FEATURE_qml_animation:INTERNAL=ON
//Qt feature: qml_debug (from target Qt6::Qml)
QT_FEATURE_qml_debug:INTERNAL=ON
//Qt feature: qml_delegate_model (from target Qt6::QmlModels)
QT_FEATURE_qml_delegate_model:INTERNAL=ON
//Qt feature: qml_itemmodel (from target Qt6::Qml)
QT_FEATURE_qml_itemmodel:INTERNAL=ON
//Qt feature: qml_jit (from target Qt6::Qml)
QT_FEATURE_qml_jit:INTERNAL=OFF
//Qt feature: qml_list_model (from target Qt6::QmlModels)
QT_FEATURE_qml_list_model:INTERNAL=ON
//Qt feature: qml_locale (from target Qt6::Qml)
QT_FEATURE_qml_locale:INTERNAL=ON
//Qt feature: qml_network (from target Qt6::Qml)
QT_FEATURE_qml_network:INTERNAL=ON
//Qt feature: qml_object_model (from target Qt6::QmlModels)
QT_FEATURE_qml_object_model:INTERNAL=ON
//Qt feature: qml_preview (from target Qt6::Qml)
QT_FEATURE_qml_preview:INTERNAL=ON
//Qt feature: qml_profiler (from target Qt6::Qml)
QT_FEATURE_qml_profiler:INTERNAL=ON
//Qt feature: qml_python (from target Qt6::Qml)
QT_FEATURE_qml_python:INTERNAL=ON
//Qt feature: qml_ssl (from target Qt6::Qml)
QT_FEATURE_qml_ssl:INTERNAL=ON
//Qt feature: qml_table_model (from target Qt6::QmlModels)
QT_FEATURE_qml_table_model:INTERNAL=ON
//Qt feature: qml_type_loader_thread (from target Qt6::Qml)
QT_FEATURE_qml_type_loader_thread:INTERNAL=ON
//Qt feature: qml_worker_script (from target Qt6::Qml)
QT_FEATURE_qml_worker_script:INTERNAL=ON
//Qt feature: qml_xml_http_request (from target Qt6::Qml)
QT_FEATURE_qml_xml_http_request:INTERNAL=ON
//Qt feature: qml_xmllistmodel (from target Qt6::Qml)
QT_FEATURE_qml_xmllistmodel:INTERNAL=ON
//Qt feature: qqnx_imf (from target Qt6::Gui)
QT_FEATURE_qqnx_imf:INTERNAL=OFF
//Qt feature: qqnx_pps (from target Qt6::Core)
QT_FEATURE_qqnx_pps:INTERNAL=OFF
//Qt feature: qt_framework (from target Qt6::Core)
QT_FEATURE_qt_framework:INTERNAL=ON
//Qt feature: qtgui_threadpool (from target Qt6::Gui)
QT_FEATURE_qtgui_threadpool:INTERNAL=ON
//Qt feature: quick_animatedimage (from target Qt6::Quick)
QT_FEATURE_quick_animatedimage:INTERNAL=ON
//Qt feature: quick_canvas (from target Qt6::Quick)
QT_FEATURE_quick_canvas:INTERNAL=ON
//Qt feature: quick_designer (from target Qt6::Quick)
QT_FEATURE_quick_designer:INTERNAL=ON
//Qt feature: quick_draganddrop (from target Qt6::Quick)
QT_FEATURE_quick_draganddrop:INTERNAL=ON
//Qt feature: quick_flipable (from target Qt6::Quick)
QT_FEATURE_quick_flipable:INTERNAL=ON
//Qt feature: quick_gridview (from target Qt6::Quick)
QT_FEATURE_quick_gridview:INTERNAL=ON
//Qt feature: quick_itemview (from target Qt6::Quick)
QT_FEATURE_quick_itemview:INTERNAL=ON
//Qt feature: quick_listview (from target Qt6::Quick)
QT_FEATURE_quick_listview:INTERNAL=ON
//Qt feature: quick_particles (from target Qt6::Quick)
QT_FEATURE_quick_particles:INTERNAL=ON
//Qt feature: quick_path (from target Qt6::Quick)
QT_FEATURE_quick_path:INTERNAL=ON
//Qt feature: quick_pathview (from target Qt6::Quick)
QT_FEATURE_quick_pathview:INTERNAL=ON
//Qt feature: quick_pixmap_cache_threaded_download (from target
// Qt6::Quick)
QT_FEATURE_quick_pixmap_cache_threaded_download:INTERNAL=ON
//Qt feature: quick_positioners (from target Qt6::Quick)
QT_FEATURE_quick_positioners:INTERNAL=ON
//Qt feature: quick_repeater (from target Qt6::Quick)
QT_FEATURE_quick_repeater:INTERNAL=ON
//Qt feature: quick_shadereffect (from target Qt6::Quick)
QT_FEATURE_quick_shadereffect:INTERNAL=ON
//Qt feature: quick_sprite (from target Qt6::Quick)
QT_FEATURE_quick_sprite:INTERNAL=ON
//Qt feature: quick_tableview (from target Qt6::Quick)
QT_FEATURE_quick_tableview:INTERNAL=ON
//Qt feature: quick_treeview (from target Qt6::Quick)
QT_FEATURE_quick_treeview:INTERNAL=ON
//Qt feature: quick_viewtransitions (from target Qt6::Quick)
QT_FEATURE_quick_viewtransitions:INTERNAL=ON
//Qt feature: radiobutton (from target Qt6::Widgets)
QT_FEATURE_radiobutton:INTERNAL=ON
//Qt feature: raster_64bit (from target Qt6::Gui)
QT_FEATURE_raster_64bit:INTERNAL=ON
//Qt feature: raster_fp (from target Qt6::Gui)
QT_FEATURE_raster_fp:INTERNAL=ON
//Qt feature: rdrnd (from target Qt6::Core)
QT_FEATURE_rdrnd:INTERNAL=OFF
//Qt feature: rdseed (from target Qt6::Core)
QT_FEATURE_rdseed:INTERNAL=OFF
//Qt feature: reduce_exports (from target Qt6::Core)
QT_FEATURE_reduce_exports:INTERNAL=ON
//Qt feature: reduce_relocations (from target Qt6::Core)
QT_FEATURE_reduce_relocations:INTERNAL=OFF
//Qt feature: regularexpression (from target Qt6::Core)
QT_FEATURE_regularexpression:INTERNAL=ON
//Qt feature: relocatable (from target Qt6::Core)
QT_FEATURE_relocatable:INTERNAL=OFF
//Qt feature: relro_now_linker (from target Qt6::Core)
QT_FEATURE_relro_now_linker:INTERNAL=OFF
//Qt feature: renameat2 (from target Qt6::Core)
QT_FEATURE_renameat2:INTERNAL=OFF
//Qt feature: res_setservers (from target Qt6::Network)
QT_FEATURE_res_setservers:INTERNAL=ON
//Qt feature: resizehandler (from target Qt6::Widgets)
QT_FEATURE_resizehandler:INTERNAL=ON
//Qt feature: rpath (from target Qt6::Core)
QT_FEATURE_rpath:INTERNAL=ON
//Qt feature: rubberband (from target Qt6::Widgets)
QT_FEATURE_rubberband:INTERNAL=ON
//Qt feature: schannel (from target Qt6::Network)
QT_FEATURE_schannel:INTERNAL=OFF
//Qt feature: scrollarea (from target Qt6::Widgets)
QT_FEATURE_scrollarea:INTERNAL=ON
//Qt feature: scrollbar (from target Qt6::Widgets)
QT_FEATURE_scrollbar:INTERNAL=ON
//Qt feature: scroller (from target Qt6::Widgets)
QT_FEATURE_scroller:INTERNAL=ON
//Qt feature: sctp (from target Qt6::Network)
QT_FEATURE_sctp:INTERNAL=OFF
//Qt feature: securetransport (from target Qt6::Network)
QT_FEATURE_securetransport:INTERNAL=ON
//Qt feature: separate_debug_info (from target Qt6::Core)
QT_FEATURE_separate_debug_info:INTERNAL=OFF
//Qt feature: sessionmanager (from target Qt6::Gui)
QT_FEATURE_sessionmanager:INTERNAL=ON
//Qt feature: settings (from target Qt6::Core)
QT_FEATURE_settings:INTERNAL=ON
//Qt feature: sha3_fast (from target Qt6::Core)
QT_FEATURE_sha3_fast:INTERNAL=ON
//Qt feature: shani (from target Qt6::Core)
QT_FEATURE_shani:INTERNAL=OFF
//Qt feature: shared (from target Qt6::Core)
QT_FEATURE_shared:INTERNAL=ON
//Qt feature: sharedmemory (from target Qt6::Core)
QT_FEATURE_sharedmemory:INTERNAL=ON
//Qt feature: shortcut (from target Qt6::Core)
QT_FEATURE_shortcut:INTERNAL=ON
//Qt feature: signaling_nan (from target Qt6::Core)
QT_FEATURE_signaling_nan:INTERNAL=ON
//Qt feature: simulator_and_device (from target Qt6::Core)
QT_FEATURE_simulator_and_device:INTERNAL=OFF
//Qt feature: sizegrip (from target Qt6::Widgets)
QT_FEATURE_sizegrip:INTERNAL=ON
//Qt feature: slider (from target Qt6::Widgets)
QT_FEATURE_slider:INTERNAL=ON
//Qt feature: slog2 (from target Qt6::Core)
QT_FEATURE_slog2:INTERNAL=OFF
//Qt feature: socks5 (from target Qt6::Network)
QT_FEATURE_socks5:INTERNAL=ON
//Qt feature: sortfilterproxymodel (from target Qt6::Core)
QT_FEATURE_sortfilterproxymodel:INTERNAL=ON
//Qt feature: spinbox (from target Qt6::Widgets)
QT_FEATURE_spinbox:INTERNAL=ON
//Qt feature: splashscreen (from target Qt6::Widgets)
QT_FEATURE_splashscreen:INTERNAL=ON
//Qt feature: splitter (from target Qt6::Widgets)
QT_FEATURE_splitter:INTERNAL=ON
//Qt feature: sql (from target Qt6::Core)
QT_FEATURE_sql:INTERNAL=ON
//Qt feature: sse2 (from target Qt6::Core)
QT_FEATURE_sse2:INTERNAL=OFF
//Qt feature: sse3 (from target Qt6::Core)
QT_FEATURE_sse3:INTERNAL=OFF
//Qt feature: sse4_1 (from target Qt6::Core)
QT_FEATURE_sse4_1:INTERNAL=OFF
//Qt feature: sse4_2 (from target Qt6::Core)
QT_FEATURE_sse4_2:INTERNAL=OFF
//Qt feature: ssl (from target Qt6::Network)
QT_FEATURE_ssl:INTERNAL=ON
//Qt feature: sspi (from target Qt6::Network)
QT_FEATURE_sspi:INTERNAL=OFF
//Qt feature: ssse3 (from target Qt6::Core)
QT_FEATURE_ssse3:INTERNAL=OFF
//Qt feature: stack_clash_protection (from target Qt6::Core)
QT_FEATURE_stack_clash_protection:INTERNAL=OFF
//Qt feature: stack_protector (from target Qt6::Core)
QT_FEATURE_stack_protector:INTERNAL=ON
//Qt feature: stackedwidget (from target Qt6::Widgets)
QT_FEATURE_stackedwidget:INTERNAL=ON
//Qt feature: standarditemmodel (from target Qt6::Gui)
QT_FEATURE_standarditemmodel:INTERNAL=ON
//Qt feature: static (from target Qt6::Core)
QT_FEATURE_static:INTERNAL=OFF
//Qt feature: statusbar (from target Qt6::Widgets)
QT_FEATURE_statusbar:INTERNAL=ON
//Qt feature: statustip (from target Qt6::Widgets)
QT_FEATURE_statustip:INTERNAL=ON
//Qt feature: std_atomic64 (from target Qt6::Core)
QT_FEATURE_std_atomic64:INTERNAL=ON
//Qt feature: stdlib_libcpp (from target Qt6::Core)
QT_FEATURE_stdlib_libcpp:INTERNAL=OFF
//Qt feature: stringlistmodel (from target Qt6::Core)
QT_FEATURE_stringlistmodel:INTERNAL=ON
//Qt feature: style_android (from target Qt6::Widgets)
QT_FEATURE_style_android:INTERNAL=OFF
//Qt feature: style_fusion (from target Qt6::Widgets)
QT_FEATURE_style_fusion:INTERNAL=ON
//Qt feature: style_mac (from target Qt6::Widgets)
QT_FEATURE_style_mac:INTERNAL=ON
//Qt feature: style_stylesheet (from target Qt6::Widgets)
QT_FEATURE_style_stylesheet:INTERNAL=ON
//Qt feature: style_windows (from target Qt6::Widgets)
QT_FEATURE_style_windows:INTERNAL=ON
//Qt feature: style_windows11 (from target Qt6::Widgets)
QT_FEATURE_style_windows11:INTERNAL=OFF
//Qt feature: style_windowsvista (from target Qt6::Widgets)
QT_FEATURE_style_windowsvista:INTERNAL=OFF
//Qt feature: syntaxhighlighter (from target Qt6::Widgets)
QT_FEATURE_syntaxhighlighter:INTERNAL=ON
//Qt feature: syslog (from target Qt6::Core)
QT_FEATURE_syslog:INTERNAL=OFF
//Qt feature: system_doubleconversion (from target Qt6::Core)
QT_FEATURE_system_doubleconversion:INTERNAL=ON
//Qt feature: system_freetype (from target Qt6::Gui)
QT_FEATURE_system_freetype:INTERNAL=ON
//Qt feature: system_harfbuzz (from target Qt6::Gui)
QT_FEATURE_system_harfbuzz:INTERNAL=ON
//Qt feature: system_jpeg (from target Qt6::Gui)
QT_FEATURE_system_jpeg:INTERNAL=ON
//Qt feature: system_libb2 (from target Qt6::Core)
QT_FEATURE_system_libb2:INTERNAL=ON
//Qt feature: system_pcre2 (from target Qt6::Core)
QT_FEATURE_system_pcre2:INTERNAL=ON
//Qt feature: system_png (from target Qt6::Gui)
QT_FEATURE_system_png:INTERNAL=ON
//Qt feature: system_proxies (from target Qt6::Network)
QT_FEATURE_system_proxies:INTERNAL=ON
//Qt feature: system_textmarkdownreader (from target Qt6::Gui)
QT_FEATURE_system_textmarkdownreader:INTERNAL=ON
//Qt feature: system_xcb_xinput (from target Qt6::Gui)
QT_FEATURE_system_xcb_xinput:INTERNAL=OFF
//Qt feature: system_zlib (from target Qt6::Core)
QT_FEATURE_system_zlib:INTERNAL=ON
//Qt feature: systemsemaphore (from target Qt6::Core)
QT_FEATURE_systemsemaphore:INTERNAL=ON
//Qt feature: systemtrayicon (from target Qt6::Gui)
QT_FEATURE_systemtrayicon:INTERNAL=ON
//Qt feature: sysv_sem (from target Qt6::Core)
QT_FEATURE_sysv_sem:INTERNAL=ON
//Qt feature: sysv_shm (from target Qt6::Core)
QT_FEATURE_sysv_shm:INTERNAL=ON
//Qt feature: tabbar (from target Qt6::Widgets)
QT_FEATURE_tabbar:INTERNAL=ON
//Qt feature: tabletevent (from target Qt6::Gui)
QT_FEATURE_tabletevent:INTERNAL=ON
//Qt feature: tableview (from target Qt6::Widgets)
QT_FEATURE_tableview:INTERNAL=ON
//Qt feature: tablewidget (from target Qt6::Widgets)
QT_FEATURE_tablewidget:INTERNAL=ON
//Qt feature: tabwidget (from target Qt6::Widgets)
QT_FEATURE_tabwidget:INTERNAL=ON
//Qt feature: temporaryfile (from target Qt6::Core)
QT_FEATURE_temporaryfile:INTERNAL=ON
//Qt feature: test_gui (from target Qt6::Core)
QT_FEATURE_test_gui:INTERNAL=ON
//Qt feature: testlib (from target Qt6::Core)
QT_FEATURE_testlib:INTERNAL=ON
//Qt feature: textbrowser (from target Qt6::Widgets)
QT_FEATURE_textbrowser:INTERNAL=ON
//Qt feature: textdate (from target Qt6::Core)
QT_FEATURE_textdate:INTERNAL=ON
//Qt feature: textedit (from target Qt6::Widgets)
QT_FEATURE_textedit:INTERNAL=ON
//Qt feature: texthtmlparser (from target Qt6::Gui)
QT_FEATURE_texthtmlparser:INTERNAL=ON
//Qt feature: textmarkdownreader (from target Qt6::Gui)
QT_FEATURE_textmarkdownreader:INTERNAL=ON
//Qt feature: textmarkdownwriter (from target Qt6::Gui)
QT_FEATURE_textmarkdownwriter:INTERNAL=ON
//Qt feature: textodfwriter (from target Qt6::Gui)
QT_FEATURE_textodfwriter:INTERNAL=ON
//Qt feature: thread (from target Qt6::Core)
QT_FEATURE_thread:INTERNAL=ON
//Qt feature: timezone (from target Qt6::Core)
QT_FEATURE_timezone:INTERNAL=ON
//Qt feature: timezone_locale (from target Qt6::Core)
QT_FEATURE_timezone_locale:INTERNAL=OFF
//Qt feature: timezone_tzdb (from target Qt6::Core)
QT_FEATURE_timezone_tzdb:INTERNAL=OFF
//Qt feature: toolbar (from target Qt6::Widgets)
QT_FEATURE_toolbar:INTERNAL=ON
//Qt feature: toolbox (from target Qt6::Widgets)
QT_FEATURE_toolbox:INTERNAL=ON
//Qt feature: toolbutton (from target Qt6::Widgets)
QT_FEATURE_toolbutton:INTERNAL=ON
//Qt feature: tooltip (from target Qt6::Widgets)
QT_FEATURE_tooltip:INTERNAL=ON
//Qt feature: topleveldomain (from target Qt6::Network)
QT_FEATURE_topleveldomain:INTERNAL=ON
//Qt feature: translation (from target Qt6::Core)
QT_FEATURE_translation:INTERNAL=ON
//Qt feature: transposeproxymodel (from target Qt6::Core)
QT_FEATURE_transposeproxymodel:INTERNAL=ON
//Qt feature: treeview (from target Qt6::Widgets)
QT_FEATURE_treeview:INTERNAL=ON
//Qt feature: treewidget (from target Qt6::Widgets)
QT_FEATURE_treewidget:INTERNAL=ON
//Qt feature: trivial_auto_var_init_pattern (from target Qt6::Core)
QT_FEATURE_trivial_auto_var_init_pattern:INTERNAL=ON
//Qt feature: tslib (from target Qt6::Gui)
QT_FEATURE_tslib:INTERNAL=OFF
//Qt feature: tuiotouch (from target Qt6::Gui)
QT_FEATURE_tuiotouch:INTERNAL=ON
//Qt feature: udpsocket (from target Qt6::Network)
QT_FEATURE_udpsocket:INTERNAL=ON
//Qt feature: undocommand (from target Qt6::Gui)
QT_FEATURE_undocommand:INTERNAL=ON
//Qt feature: undogroup (from target Qt6::Gui)
QT_FEATURE_undogroup:INTERNAL=ON
//Qt feature: undostack (from target Qt6::Gui)
QT_FEATURE_undostack:INTERNAL=ON
//Qt feature: undoview (from target Qt6::Widgets)
QT_FEATURE_undoview:INTERNAL=ON
//Qt feature: use_bfd_linker (from target Qt6::Core)
QT_FEATURE_use_bfd_linker:INTERNAL=OFF
//Qt feature: use_gold_linker (from target Qt6::Core)
QT_FEATURE_use_gold_linker:INTERNAL=OFF
//Qt feature: use_lld_linker (from target Qt6::Core)
QT_FEATURE_use_lld_linker:INTERNAL=OFF
//Qt feature: use_mold_linker (from target Qt6::Core)
QT_FEATURE_use_mold_linker:INTERNAL=OFF
//Qt feature: vaes (from target Qt6::Core)
QT_FEATURE_vaes:INTERNAL=OFF
//Qt feature: validator (from target Qt6::Gui)
QT_FEATURE_validator:INTERNAL=ON
//Qt feature: version_tagging (from target Qt6::Core)
QT_FEATURE_version_tagging:INTERNAL=ON
//Qt feature: vkgen (from target Qt6::Gui)
QT_FEATURE_vkgen:INTERNAL=ON
//Qt feature: vkkhrdisplay (from target Qt6::Gui)
QT_FEATURE_vkkhrdisplay:INTERNAL=OFF
//Qt feature: vnc (from target Qt6::Gui)
QT_FEATURE_vnc:INTERNAL=OFF
//Qt feature: vsp2 (from target Qt6::Gui)
QT_FEATURE_vsp2:INTERNAL=OFF
//Qt feature: vulkan (from target Qt6::Gui)
QT_FEATURE_vulkan:INTERNAL=ON
//Qt feature: vxworksevdev (from target Qt6::Gui)
QT_FEATURE_vxworksevdev:INTERNAL=OFF
//Qt feature: wasm_exceptions (from target Qt6::Core)
QT_FEATURE_wasm_exceptions:INTERNAL=OFF
//Qt feature: wasm_jspi (from target Qt6::Core)
QT_FEATURE_wasm_jspi:INTERNAL=OFF
//Qt feature: wasm_simd128 (from target Qt6::Core)
QT_FEATURE_wasm_simd128:INTERNAL=OFF
//Qt feature: wayland (from target Qt6::Gui)
QT_FEATURE_wayland:INTERNAL=OFF
//Qt feature: webengine_arm64_udot_support (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_arm64_udot_support:INTERNAL=OFF
//Qt feature: webengine_embedded_build (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_embedded_build:INTERNAL=OFF
//Qt feature: webengine_extensions (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_extensions:INTERNAL=ON
//Qt feature: webengine_full_debug_info (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_full_debug_info:INTERNAL=OFF
//Qt feature: webengine_geolocation (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_geolocation:INTERNAL=ON
//Qt feature: webengine_kerberos (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_kerberos:INTERNAL=ON
//Qt feature: webengine_native_spellchecker (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_native_spellchecker:INTERNAL=OFF
//Qt feature: webengine_pepper_plugins (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_pepper_plugins:INTERNAL=ON
//Qt feature: webengine_printing_and_pdf (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_printing_and_pdf:INTERNAL=ON
//Qt feature: webengine_proprietary_codecs (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_proprietary_codecs:INTERNAL=ON
//Qt feature: webengine_sanitizer (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_sanitizer:INTERNAL=OFF
//Qt feature: webengine_spellchecker (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_spellchecker:INTERNAL=ON
//Qt feature: webengine_system_alsa (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_system_alsa:INTERNAL=OFF
//Qt feature: webengine_system_gbm (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_system_gbm:INTERNAL=OFF
//Qt feature: webengine_system_pulseaudio (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_system_pulseaudio:INTERNAL=OFF
//Qt feature: webengine_v8_context_snapshot (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_v8_context_snapshot:INTERNAL=ON
//Qt feature: webengine_vaapi (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_vaapi:INTERNAL=OFF
//Qt feature: webengine_vulkan (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_vulkan:INTERNAL=OFF
//Qt feature: webengine_webchannel (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_webchannel:INTERNAL=ON
//Qt feature: webengine_webrtc (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_webrtc:INTERNAL=ON
//Qt feature: webengine_webrtc_pipewire (from target Qt6::WebEngineCore)
QT_FEATURE_webengine_webrtc_pipewire:INTERNAL=OFF
//Qt feature: webenginedriver (from target Qt6::WebEngineCore)
QT_FEATURE_webenginedriver:INTERNAL=ON
//Qt feature: whatsthis (from target Qt6::Gui)
QT_FEATURE_whatsthis:INTERNAL=ON
//Qt feature: wheelevent (from target Qt6::Gui)
QT_FEATURE_wheelevent:INTERNAL=ON
//Qt feature: widgets (from target Qt6::Core)
QT_FEATURE_widgets:INTERNAL=ON
//Qt feature: widgettextcontrol (from target Qt6::Widgets)
QT_FEATURE_widgettextcontrol:INTERNAL=ON
//Qt feature: winrt_geolocation (from target Qt6::Positioning)
QT_FEATURE_winrt_geolocation:INTERNAL=OFF
//Qt feature: wizard (from target Qt6::Widgets)
QT_FEATURE_wizard:INTERNAL=ON
//Qt feature: x86intrin (from target Qt6::Core)
QT_FEATURE_x86intrin:INTERNAL=OFF
//Qt feature: xcb (from target Qt6::Gui)
QT_FEATURE_xcb:INTERNAL=OFF
//Qt feature: xcb_egl_plugin (from target Qt6::Gui)
QT_FEATURE_xcb_egl_plugin:INTERNAL=OFF
//Qt feature: xcb_glx (from target Qt6::Gui)
QT_FEATURE_xcb_glx:INTERNAL=OFF
//Qt feature: xcb_glx_plugin (from target Qt6::Gui)
QT_FEATURE_xcb_glx_plugin:INTERNAL=OFF
//Qt feature: xcb_native_painting (from target Qt6::Gui)
QT_FEATURE_xcb_native_painting:INTERNAL=OFF
//Qt feature: xcb_sm (from target Qt6::Gui)
QT_FEATURE_xcb_sm:INTERNAL=OFF
//Qt feature: xcb_xlib (from target Qt6::Gui)
QT_FEATURE_xcb_xlib:INTERNAL=OFF
//Qt feature: xkbcommon (from target Qt6::Gui)
QT_FEATURE_xkbcommon:INTERNAL=OFF
//Qt feature: xkbcommon_x11 (from target Qt6::Gui)
QT_FEATURE_xkbcommon_x11:INTERNAL=OFF
//Qt feature: xlib (from target Qt6::Gui)
QT_FEATURE_xlib:INTERNAL=OFF
//Qt feature: xml (from target Qt6::Core)
QT_FEATURE_xml:INTERNAL=ON
//Qt feature: xmlstream (from target Qt6::Core)
QT_FEATURE_xmlstream:INTERNAL=ON
//Qt feature: xmlstreamreader (from target Qt6::Core)
QT_FEATURE_xmlstreamreader:INTERNAL=ON
//Qt feature: xmlstreamwriter (from target Qt6::Core)
QT_FEATURE_xmlstreamwriter:INTERNAL=ON
//Qt feature: xrender (from target Qt6::Gui)
QT_FEATURE_xrender:INTERNAL=OFF
//Qt feature: zstd (from target Qt6::Core)
QT_FEATURE_zstd:INTERNAL=ON
//ADVANCED property for variable: Vulkan_GLSLANG_VALIDATOR_EXECUTABLE
Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_GLSLC_EXECUTABLE
Vulkan_GLSLC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_INCLUDE_DIR
Vulkan_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_LIBRARY
Vulkan_LIBRARY-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=/usr/local
__qt_qml_macros_module_base_dir:INTERNAL=/opt/homebrew/opt/qt@6/lib/cmake/Qt6Qml

