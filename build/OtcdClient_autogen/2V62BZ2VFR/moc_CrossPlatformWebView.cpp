/****************************************************************************
** Meta object code from reading C++ file 'CrossPlatformWebView.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../src/util/CrossPlatformWebView.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'CrossPlatformWebView.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN20CrossPlatformWebViewE_t {};
} // unnamed namespace

template <> constexpr inline auto CrossPlatformWebView::qt_create_metaobjectdata<qt_meta_tag_ZN20CrossPlatformWebViewE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "CrossPlatformWebView",
        "loadStarted",
        "",
        "loadProgress",
        "progress",
        "loadFinished",
        "success",
        "titleChanged",
        "title",
        "urlChanged",
        "url",
        "iconChanged",
        "icon",
        "onLoadStarted",
        "onLoadProgress",
        "onLoadFinished",
        "onTitleChanged",
        "onUrlChanged",
        "onIconChanged"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'loadStarted'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'loadProgress'
        QtMocHelpers::SignalData<void(int)>(3, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Int, 4 },
        }}),
        // Signal 'loadFinished'
        QtMocHelpers::SignalData<void(bool)>(5, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::Bool, 6 },
        }}),
        // Signal 'titleChanged'
        QtMocHelpers::SignalData<void(const QString &)>(7, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QString, 8 },
        }}),
        // Signal 'urlChanged'
        QtMocHelpers::SignalData<void(const QUrl &)>(9, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QUrl, 10 },
        }}),
        // Signal 'iconChanged'
        QtMocHelpers::SignalData<void(const QIcon &)>(11, 2, QMC::AccessPublic, QMetaType::Void, {{
            { QMetaType::QIcon, 12 },
        }}),
        // Slot 'onLoadStarted'
        QtMocHelpers::SlotData<void()>(13, 2, QMC::AccessPrivate, QMetaType::Void),
        // Slot 'onLoadProgress'
        QtMocHelpers::SlotData<void(int)>(14, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Int, 4 },
        }}),
        // Slot 'onLoadFinished'
        QtMocHelpers::SlotData<void(bool)>(15, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::Bool, 6 },
        }}),
        // Slot 'onTitleChanged'
        QtMocHelpers::SlotData<void(const QString &)>(16, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QString, 8 },
        }}),
        // Slot 'onUrlChanged'
        QtMocHelpers::SlotData<void(const QUrl &)>(17, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QUrl, 10 },
        }}),
        // Slot 'onIconChanged'
        QtMocHelpers::SlotData<void(const QIcon &)>(18, 2, QMC::AccessPrivate, QMetaType::Void, {{
            { QMetaType::QIcon, 12 },
        }}),
    };
    QtMocHelpers::UintData qt_properties {
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<CrossPlatformWebView, qt_meta_tag_ZN20CrossPlatformWebViewE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject CrossPlatformWebView::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20CrossPlatformWebViewE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20CrossPlatformWebViewE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN20CrossPlatformWebViewE_t>.metaTypes,
    nullptr
} };

void CrossPlatformWebView::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<CrossPlatformWebView *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->loadStarted(); break;
        case 1: _t->loadProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 2: _t->loadFinished((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 3: _t->titleChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->urlChanged((*reinterpret_cast< std::add_pointer_t<QUrl>>(_a[1]))); break;
        case 5: _t->iconChanged((*reinterpret_cast< std::add_pointer_t<QIcon>>(_a[1]))); break;
        case 6: _t->onLoadStarted(); break;
        case 7: _t->onLoadProgress((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 8: _t->onLoadFinished((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1]))); break;
        case 9: _t->onTitleChanged((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 10: _t->onUrlChanged((*reinterpret_cast< std::add_pointer_t<QUrl>>(_a[1]))); break;
        case 11: _t->onIconChanged((*reinterpret_cast< std::add_pointer_t<QIcon>>(_a[1]))); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (CrossPlatformWebView::*)()>(_a, &CrossPlatformWebView::loadStarted, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (CrossPlatformWebView::*)(int )>(_a, &CrossPlatformWebView::loadProgress, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (CrossPlatformWebView::*)(bool )>(_a, &CrossPlatformWebView::loadFinished, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (CrossPlatformWebView::*)(const QString & )>(_a, &CrossPlatformWebView::titleChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (CrossPlatformWebView::*)(const QUrl & )>(_a, &CrossPlatformWebView::urlChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (CrossPlatformWebView::*)(const QIcon & )>(_a, &CrossPlatformWebView::iconChanged, 5))
            return;
    }
}

const QMetaObject *CrossPlatformWebView::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *CrossPlatformWebView::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20CrossPlatformWebViewE_t>.strings))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int CrossPlatformWebView::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 12;
    }
    return _id;
}

// SIGNAL 0
void CrossPlatformWebView::loadStarted()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void CrossPlatformWebView::loadProgress(int _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 1, nullptr, _t1);
}

// SIGNAL 2
void CrossPlatformWebView::loadFinished(bool _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 2, nullptr, _t1);
}

// SIGNAL 3
void CrossPlatformWebView::titleChanged(const QString & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 3, nullptr, _t1);
}

// SIGNAL 4
void CrossPlatformWebView::urlChanged(const QUrl & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 4, nullptr, _t1);
}

// SIGNAL 5
void CrossPlatformWebView::iconChanged(const QIcon & _t1)
{
    QMetaObject::activate<void>(this, &staticMetaObject, 5, nullptr, _t1);
}
QT_WARNING_POP
