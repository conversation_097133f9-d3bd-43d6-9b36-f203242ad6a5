
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/sy&xy/code/self/C++/otc/build/OtcdClient_autogen/mocs_compilation.cpp" "CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o" "gcc" "CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o.d"
  "/Users/<USER>/sy&xy/code/self/C++/otc/src/app/main.cpp" "CMakeFiles/OtcdClient.dir/src/app/main.cpp.o" "gcc" "CMakeFiles/OtcdClient.dir/src/app/main.cpp.o.d"
  "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/ConfigDialog.cpp" "CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o" "gcc" "CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o.d"
  "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/Console.cpp" "CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o" "gcc" "CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o.d"
  "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/DefaultLayout.cpp" "CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o" "gcc" "CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o.d"
  "/Users/<USER>/sy&xy/code/self/C++/otc/src/module/EmptyPanel.cpp" "CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o" "gcc" "CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o.d"
  "/Users/<USER>/sy&xy/code/self/C++/otc/src/module/TablePanel.cpp" "CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o" "gcc" "CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o.d"
  "/Users/<USER>/sy&xy/code/self/C++/otc/src/util/CrossPlatformWebView.cpp" "CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o" "gcc" "CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
