# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_NO_DEBUG -DQT_OPENGL_LIB -DQT_POSITIONING_LIB -DQT_PRINTSUPPORT_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DQT_WEBCHANNEL_LIB -DQT_WEBENGINECORE_LIB -DQT_WEBENGINEWIDGETS_LIB -DQT_WIDGETS_LIB

CXX_INCLUDES = -I"/Users/<USER>/sy&xy/code/self/C++/otc/build" -I"/Users/<USER>/sy&xy/code/self/C++/otc" -I"/Users/<USER>/sy&xy/code/self/C++/otc/build/OtcdClient_autogen/include" -I"/Users/<USER>/sy&xy/code/self/C++/otc/src/console" -I"/Users/<USER>/sy&xy/code/self/C++/otc/src/module" -I"/Users/<USER>/sy&xy/code/self/C++/otc/src/util" -isystem /opt/homebrew/opt/qt@6/lib/QtWidgets.framework/Headers -iframework /opt/homebrew/opt/qt@6/lib -isystem /opt/homebrew/opt/qt@6/lib/QtCore.framework/Headers -isystem /opt/homebrew/opt/qt@6/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/opt/qt@6/include -isystem /opt/homebrew/opt/qt@6/lib/QtGui.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtWebEngineWidgets.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtWebEngineCore.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtNetwork.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtQuick.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtQml.framework/Headers -isystem /opt/homebrew/opt/qt@6/include/QtQmlIntegration -isystem /opt/homebrew/opt/qt@6/lib/QtQmlMeta.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtQmlModels.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtQmlWorkerScript.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtOpenGL.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtWebChannel.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtPositioning.framework/Headers -isystem /opt/homebrew/opt/qt@6/lib/QtPrintSupport.framework/Headers

CXX_FLAGSarm64 = -std=gnu++17 -arch arm64

CXX_FLAGS = -std=gnu++17 -arch arm64

