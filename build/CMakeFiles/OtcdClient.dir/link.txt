/usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o CMakeFiles/OtcdClient.dir/src/app/main.cpp.o CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o -o OtcdClient -F/opt/homebrew/opt/qt@6/lib  -Wl,-rpath,/opt/homebrew/opt/qt@6/lib /opt/homebrew/opt/qt@6/lib/QtWebEngineWidgets.framework/Versions/A/QtWebEngineWidgets /opt/homebrew/opt/qt@6/lib/QtWebEngineCore.framework/Versions/A/QtWebEngineCore /opt/homebrew/opt/qt@6/lib/QtWebChannel.framework/Versions/A/QtWebChannel /opt/homebrew/opt/qt@6/lib/QtPositioning.framework/Versions/A/QtPositioning /opt/homebrew/opt/qt@6/lib/QtPrintSupport.framework/Versions/A/QtPrintSupport -framework ApplicationServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libcups.tbd /opt/homebrew/opt/qt@6/lib/QtWidgets.framework/Versions/A/QtWidgets /opt/homebrew/opt/qt@6/lib/QtQuick.framework/Versions/A/QtQuick /opt/homebrew/opt/qt@6/lib/QtQmlMeta.framework/Versions/A/QtQmlMeta /opt/homebrew/opt/qt@6/lib/QtQmlWorkerScript.framework/Versions/A/QtQmlWorkerScript /opt/homebrew/opt/qt@6/lib/QtQmlModels.framework/Versions/A/QtQmlModels /opt/homebrew/opt/qt@6/lib/QtOpenGL.framework/Versions/A/QtOpenGL /opt/homebrew/opt/qt@6/lib/QtGui.framework/Versions/A/QtGui -framework AGL -framework AppKit -framework OpenGL -framework ImageIO -framework Metal /opt/homebrew/opt/qt@6/lib/QtQml.framework/Versions/A/QtQml /opt/homebrew/opt/qt@6/lib/QtNetwork.framework/Versions/A/QtNetwork /opt/homebrew/opt/qt@6/lib/QtCore.framework/Versions/A/QtCore -framework IOKit -framework DiskArbitration -framework UniformTypeIdentifiers
