# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "/Users/<USER>/sy&xy/code/self/C++/otc"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "/Users/<USER>/sy&xy/code/self/C++/otc/build"

# Include any dependencies generated for this target.
include CMakeFiles/OtcdClient.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/OtcdClient.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/OtcdClient.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/OtcdClient.dir/flags.make

CMakeFiles/OtcdClient.dir/codegen:
.PHONY : CMakeFiles/OtcdClient.dir/codegen

CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o: CMakeFiles/OtcdClient.dir/flags.make
CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o: OtcdClient_autogen/mocs_compilation.cpp
CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o: CMakeFiles/OtcdClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o -MF CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o -c "/Users/<USER>/sy&xy/code/self/C++/otc/build/OtcdClient_autogen/mocs_compilation.cpp"

CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/Users/<USER>/sy&xy/code/self/C++/otc/build/OtcdClient_autogen/mocs_compilation.cpp" > CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.i

CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/Users/<USER>/sy&xy/code/self/C++/otc/build/OtcdClient_autogen/mocs_compilation.cpp" -o CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.s

CMakeFiles/OtcdClient.dir/src/app/main.cpp.o: CMakeFiles/OtcdClient.dir/flags.make
CMakeFiles/OtcdClient.dir/src/app/main.cpp.o: /Users/<USER>/sy&xy/code/self/C++/otc/src/app/main.cpp
CMakeFiles/OtcdClient.dir/src/app/main.cpp.o: CMakeFiles/OtcdClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/OtcdClient.dir/src/app/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/OtcdClient.dir/src/app/main.cpp.o -MF CMakeFiles/OtcdClient.dir/src/app/main.cpp.o.d -o CMakeFiles/OtcdClient.dir/src/app/main.cpp.o -c "/Users/<USER>/sy&xy/code/self/C++/otc/src/app/main.cpp"

CMakeFiles/OtcdClient.dir/src/app/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/OtcdClient.dir/src/app/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/Users/<USER>/sy&xy/code/self/C++/otc/src/app/main.cpp" > CMakeFiles/OtcdClient.dir/src/app/main.cpp.i

CMakeFiles/OtcdClient.dir/src/app/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/OtcdClient.dir/src/app/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/Users/<USER>/sy&xy/code/self/C++/otc/src/app/main.cpp" -o CMakeFiles/OtcdClient.dir/src/app/main.cpp.s

CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o: CMakeFiles/OtcdClient.dir/flags.make
CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o: /Users/<USER>/sy&xy/code/self/C++/otc/src/console/Console.cpp
CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o: CMakeFiles/OtcdClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o -MF CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o.d -o CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o -c "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/Console.cpp"

CMakeFiles/OtcdClient.dir/src/console/Console.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/OtcdClient.dir/src/console/Console.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/Console.cpp" > CMakeFiles/OtcdClient.dir/src/console/Console.cpp.i

CMakeFiles/OtcdClient.dir/src/console/Console.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/OtcdClient.dir/src/console/Console.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/Console.cpp" -o CMakeFiles/OtcdClient.dir/src/console/Console.cpp.s

CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o: CMakeFiles/OtcdClient.dir/flags.make
CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o: /Users/<USER>/sy&xy/code/self/C++/otc/src/console/ConfigDialog.cpp
CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o: CMakeFiles/OtcdClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o -MF CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o.d -o CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o -c "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/ConfigDialog.cpp"

CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/ConfigDialog.cpp" > CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.i

CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/ConfigDialog.cpp" -o CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.s

CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o: CMakeFiles/OtcdClient.dir/flags.make
CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o: /Users/<USER>/sy&xy/code/self/C++/otc/src/console/DefaultLayout.cpp
CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o: CMakeFiles/OtcdClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o -MF CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o.d -o CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o -c "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/DefaultLayout.cpp"

CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/DefaultLayout.cpp" > CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.i

CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/Users/<USER>/sy&xy/code/self/C++/otc/src/console/DefaultLayout.cpp" -o CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.s

CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o: CMakeFiles/OtcdClient.dir/flags.make
CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o: /Users/<USER>/sy&xy/code/self/C++/otc/src/module/EmptyPanel.cpp
CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o: CMakeFiles/OtcdClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o -MF CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o.d -o CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o -c "/Users/<USER>/sy&xy/code/self/C++/otc/src/module/EmptyPanel.cpp"

CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/Users/<USER>/sy&xy/code/self/C++/otc/src/module/EmptyPanel.cpp" > CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.i

CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/Users/<USER>/sy&xy/code/self/C++/otc/src/module/EmptyPanel.cpp" -o CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.s

CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o: CMakeFiles/OtcdClient.dir/flags.make
CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o: /Users/<USER>/sy&xy/code/self/C++/otc/src/module/TablePanel.cpp
CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o: CMakeFiles/OtcdClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o -MF CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o.d -o CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o -c "/Users/<USER>/sy&xy/code/self/C++/otc/src/module/TablePanel.cpp"

CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/Users/<USER>/sy&xy/code/self/C++/otc/src/module/TablePanel.cpp" > CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.i

CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/Users/<USER>/sy&xy/code/self/C++/otc/src/module/TablePanel.cpp" -o CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.s

CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o: CMakeFiles/OtcdClient.dir/flags.make
CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o: /Users/<USER>/sy&xy/code/self/C++/otc/src/util/CrossPlatformWebView.cpp
CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o: CMakeFiles/OtcdClient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o -MF CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o.d -o CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o -c "/Users/<USER>/sy&xy/code/self/C++/otc/src/util/CrossPlatformWebView.cpp"

CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E "/Users/<USER>/sy&xy/code/self/C++/otc/src/util/CrossPlatformWebView.cpp" > CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.i

CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S "/Users/<USER>/sy&xy/code/self/C++/otc/src/util/CrossPlatformWebView.cpp" -o CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.s

# Object files for target OtcdClient
OtcdClient_OBJECTS = \
"CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/OtcdClient.dir/src/app/main.cpp.o" \
"CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o" \
"CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o" \
"CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o" \
"CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o" \
"CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o" \
"CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o"

# External object files for target OtcdClient
OtcdClient_EXTERNAL_OBJECTS =

OtcdClient: CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o
OtcdClient: CMakeFiles/OtcdClient.dir/src/app/main.cpp.o
OtcdClient: CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o
OtcdClient: CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o
OtcdClient: CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o
OtcdClient: CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o
OtcdClient: CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o
OtcdClient: CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o
OtcdClient: CMakeFiles/OtcdClient.dir/build.make
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtWebEngineWidgets.framework/Versions/A/QtWebEngineWidgets
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtWebEngineCore.framework/Versions/A/QtWebEngineCore
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtWebChannel.framework/Versions/A/QtWebChannel
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtPositioning.framework/Versions/A/QtPositioning
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtPrintSupport.framework/Versions/A/QtPrintSupport
OtcdClient: /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX.sdk/usr/lib/libcups.tbd
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtWidgets.framework/Versions/A/QtWidgets
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtQuick.framework/Versions/A/QtQuick
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtQmlMeta.framework/Versions/A/QtQmlMeta
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtQmlWorkerScript.framework/Versions/A/QtQmlWorkerScript
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtQmlModels.framework/Versions/A/QtQmlModels
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtOpenGL.framework/Versions/A/QtOpenGL
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtGui.framework/Versions/A/QtGui
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtQml.framework/Versions/A/QtQml
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtNetwork.framework/Versions/A/QtNetwork
OtcdClient: /opt/homebrew/opt/qt@6/lib/QtCore.framework/Versions/A/QtCore
OtcdClient: CMakeFiles/OtcdClient.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=$(CMAKE_PROGRESS_9) "Linking CXX executable OtcdClient"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/OtcdClient.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/OtcdClient.dir/build: OtcdClient
.PHONY : CMakeFiles/OtcdClient.dir/build

CMakeFiles/OtcdClient.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/OtcdClient.dir/cmake_clean.cmake
.PHONY : CMakeFiles/OtcdClient.dir/clean

CMakeFiles/OtcdClient.dir/depend:
	cd "/Users/<USER>/sy&xy/code/self/C++/otc/build" && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" "/Users/<USER>/sy&xy/code/self/C++/otc" "/Users/<USER>/sy&xy/code/self/C++/otc" "/Users/<USER>/sy&xy/code/self/C++/otc/build" "/Users/<USER>/sy&xy/code/self/C++/otc/build" "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles/OtcdClient.dir/DependInfo.cmake" "--color=$(COLOR)"
.PHONY : CMakeFiles/OtcdClient.dir/depend

