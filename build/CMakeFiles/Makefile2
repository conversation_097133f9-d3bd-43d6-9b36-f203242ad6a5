# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "/Users/<USER>/sy&xy/code/self/C++/otc"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "/Users/<USER>/sy&xy/code/self/C++/otc/build"

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/OtcdClient.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/OtcdClient.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/OtcdClient.dir/clean
clean: CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/OtcdClient_autogen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/OtcdClient.dir

# All Build rule for target.
CMakeFiles/OtcdClient.dir/all: CMakeFiles/OtcdClient_autogen.dir/all
CMakeFiles/OtcdClient.dir/all: CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=1,2,3,4,5,6,7,8,9 "Built target OtcdClient"
.PHONY : CMakeFiles/OtcdClient.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/OtcdClient.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/OtcdClient.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" 0
.PHONY : CMakeFiles/OtcdClient.dir/rule

# Convenience name for target.
OtcdClient: CMakeFiles/OtcdClient.dir/rule
.PHONY : OtcdClient

# codegen rule for target.
CMakeFiles/OtcdClient.dir/codegen: CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=1,2,3,4,5,6,7,8,9 "Finished codegen for target OtcdClient"
.PHONY : CMakeFiles/OtcdClient.dir/codegen

# clean rule for target.
CMakeFiles/OtcdClient.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/clean
.PHONY : CMakeFiles/OtcdClient.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/OtcdClient_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/build.make CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/build.make CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num= "Built target OtcdClient_autogen_timestamp_deps"
.PHONY : CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" 0
.PHONY : CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/rule

# Convenience name for target.
OtcdClient_autogen_timestamp_deps: CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/rule
.PHONY : OtcdClient_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/build.make CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num= "Finished codegen for target OtcdClient_autogen_timestamp_deps"
.PHONY : CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/build.make CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/OtcdClient_autogen.dir

# All Build rule for target.
CMakeFiles/OtcdClient_autogen.dir/all: CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen.dir/build.make CMakeFiles/OtcdClient_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen.dir/build.make CMakeFiles/OtcdClient_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=10 "Built target OtcdClient_autogen"
.PHONY : CMakeFiles/OtcdClient_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/OtcdClient_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/OtcdClient_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" 0
.PHONY : CMakeFiles/OtcdClient_autogen.dir/rule

# Convenience name for target.
OtcdClient_autogen: CMakeFiles/OtcdClient_autogen.dir/rule
.PHONY : OtcdClient_autogen

# codegen rule for target.
CMakeFiles/OtcdClient_autogen.dir/codegen: CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen.dir/build.make CMakeFiles/OtcdClient_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir="/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" --progress-num=10 "Finished codegen for target OtcdClient_autogen"
.PHONY : CMakeFiles/OtcdClient_autogen.dir/codegen

# clean rule for target.
CMakeFiles/OtcdClient_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen.dir/build.make CMakeFiles/OtcdClient_autogen.dir/clean
.PHONY : CMakeFiles/OtcdClient_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

