# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = "/Users/<USER>/sy&xy/code/self/C++/otc"

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = "/Users/<USER>/sy&xy/code/self/C++/otc/build"

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" "/Users/<USER>/sy&xy/code/self/C++/otc/build//CMakeFiles/progress.marks"
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start "/Users/<USER>/sy&xy/code/self/C++/otc/build/CMakeFiles" 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named OtcdClient

# Build rule for target.
OtcdClient: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 OtcdClient
.PHONY : OtcdClient

# fast build rule for target.
OtcdClient/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/build
.PHONY : OtcdClient/fast

#=============================================================================
# Target rules for targets named OtcdClient_autogen_timestamp_deps

# Build rule for target.
OtcdClient_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 OtcdClient_autogen_timestamp_deps
.PHONY : OtcdClient_autogen_timestamp_deps

# fast build rule for target.
OtcdClient_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/build.make CMakeFiles/OtcdClient_autogen_timestamp_deps.dir/build
.PHONY : OtcdClient_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named OtcdClient_autogen

# Build rule for target.
OtcdClient_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 OtcdClient_autogen
.PHONY : OtcdClient_autogen

# fast build rule for target.
OtcdClient_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient_autogen.dir/build.make CMakeFiles/OtcdClient_autogen.dir/build
.PHONY : OtcdClient_autogen/fast

OtcdClient_autogen/mocs_compilation.o: OtcdClient_autogen/mocs_compilation.cpp.o
.PHONY : OtcdClient_autogen/mocs_compilation.o

# target to build an object file
OtcdClient_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.o
.PHONY : OtcdClient_autogen/mocs_compilation.cpp.o

OtcdClient_autogen/mocs_compilation.i: OtcdClient_autogen/mocs_compilation.cpp.i
.PHONY : OtcdClient_autogen/mocs_compilation.i

# target to preprocess a source file
OtcdClient_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.i
.PHONY : OtcdClient_autogen/mocs_compilation.cpp.i

OtcdClient_autogen/mocs_compilation.s: OtcdClient_autogen/mocs_compilation.cpp.s
.PHONY : OtcdClient_autogen/mocs_compilation.s

# target to generate assembly for a file
OtcdClient_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/OtcdClient_autogen/mocs_compilation.cpp.s
.PHONY : OtcdClient_autogen/mocs_compilation.cpp.s

src/app/main.o: src/app/main.cpp.o
.PHONY : src/app/main.o

# target to build an object file
src/app/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/app/main.cpp.o
.PHONY : src/app/main.cpp.o

src/app/main.i: src/app/main.cpp.i
.PHONY : src/app/main.i

# target to preprocess a source file
src/app/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/app/main.cpp.i
.PHONY : src/app/main.cpp.i

src/app/main.s: src/app/main.cpp.s
.PHONY : src/app/main.s

# target to generate assembly for a file
src/app/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/app/main.cpp.s
.PHONY : src/app/main.cpp.s

src/console/ConfigDialog.o: src/console/ConfigDialog.cpp.o
.PHONY : src/console/ConfigDialog.o

# target to build an object file
src/console/ConfigDialog.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.o
.PHONY : src/console/ConfigDialog.cpp.o

src/console/ConfigDialog.i: src/console/ConfigDialog.cpp.i
.PHONY : src/console/ConfigDialog.i

# target to preprocess a source file
src/console/ConfigDialog.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.i
.PHONY : src/console/ConfigDialog.cpp.i

src/console/ConfigDialog.s: src/console/ConfigDialog.cpp.s
.PHONY : src/console/ConfigDialog.s

# target to generate assembly for a file
src/console/ConfigDialog.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/ConfigDialog.cpp.s
.PHONY : src/console/ConfigDialog.cpp.s

src/console/Console.o: src/console/Console.cpp.o
.PHONY : src/console/Console.o

# target to build an object file
src/console/Console.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/Console.cpp.o
.PHONY : src/console/Console.cpp.o

src/console/Console.i: src/console/Console.cpp.i
.PHONY : src/console/Console.i

# target to preprocess a source file
src/console/Console.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/Console.cpp.i
.PHONY : src/console/Console.cpp.i

src/console/Console.s: src/console/Console.cpp.s
.PHONY : src/console/Console.s

# target to generate assembly for a file
src/console/Console.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/Console.cpp.s
.PHONY : src/console/Console.cpp.s

src/console/DefaultLayout.o: src/console/DefaultLayout.cpp.o
.PHONY : src/console/DefaultLayout.o

# target to build an object file
src/console/DefaultLayout.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.o
.PHONY : src/console/DefaultLayout.cpp.o

src/console/DefaultLayout.i: src/console/DefaultLayout.cpp.i
.PHONY : src/console/DefaultLayout.i

# target to preprocess a source file
src/console/DefaultLayout.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.i
.PHONY : src/console/DefaultLayout.cpp.i

src/console/DefaultLayout.s: src/console/DefaultLayout.cpp.s
.PHONY : src/console/DefaultLayout.s

# target to generate assembly for a file
src/console/DefaultLayout.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/console/DefaultLayout.cpp.s
.PHONY : src/console/DefaultLayout.cpp.s

src/module/EmptyPanel.o: src/module/EmptyPanel.cpp.o
.PHONY : src/module/EmptyPanel.o

# target to build an object file
src/module/EmptyPanel.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.o
.PHONY : src/module/EmptyPanel.cpp.o

src/module/EmptyPanel.i: src/module/EmptyPanel.cpp.i
.PHONY : src/module/EmptyPanel.i

# target to preprocess a source file
src/module/EmptyPanel.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.i
.PHONY : src/module/EmptyPanel.cpp.i

src/module/EmptyPanel.s: src/module/EmptyPanel.cpp.s
.PHONY : src/module/EmptyPanel.s

# target to generate assembly for a file
src/module/EmptyPanel.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/module/EmptyPanel.cpp.s
.PHONY : src/module/EmptyPanel.cpp.s

src/module/TablePanel.o: src/module/TablePanel.cpp.o
.PHONY : src/module/TablePanel.o

# target to build an object file
src/module/TablePanel.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.o
.PHONY : src/module/TablePanel.cpp.o

src/module/TablePanel.i: src/module/TablePanel.cpp.i
.PHONY : src/module/TablePanel.i

# target to preprocess a source file
src/module/TablePanel.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.i
.PHONY : src/module/TablePanel.cpp.i

src/module/TablePanel.s: src/module/TablePanel.cpp.s
.PHONY : src/module/TablePanel.s

# target to generate assembly for a file
src/module/TablePanel.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/module/TablePanel.cpp.s
.PHONY : src/module/TablePanel.cpp.s

src/util/CrossPlatformWebView.o: src/util/CrossPlatformWebView.cpp.o
.PHONY : src/util/CrossPlatformWebView.o

# target to build an object file
src/util/CrossPlatformWebView.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.o
.PHONY : src/util/CrossPlatformWebView.cpp.o

src/util/CrossPlatformWebView.i: src/util/CrossPlatformWebView.cpp.i
.PHONY : src/util/CrossPlatformWebView.i

# target to preprocess a source file
src/util/CrossPlatformWebView.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.i
.PHONY : src/util/CrossPlatformWebView.cpp.i

src/util/CrossPlatformWebView.s: src/util/CrossPlatformWebView.cpp.s
.PHONY : src/util/CrossPlatformWebView.s

# target to generate assembly for a file
src/util/CrossPlatformWebView.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/OtcdClient.dir/build.make CMakeFiles/OtcdClient.dir/src/util/CrossPlatformWebView.cpp.s
.PHONY : src/util/CrossPlatformWebView.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... OtcdClient_autogen"
	@echo "... OtcdClient_autogen_timestamp_deps"
	@echo "... OtcdClient"
	@echo "... OtcdClient_autogen/mocs_compilation.o"
	@echo "... OtcdClient_autogen/mocs_compilation.i"
	@echo "... OtcdClient_autogen/mocs_compilation.s"
	@echo "... src/app/main.o"
	@echo "... src/app/main.i"
	@echo "... src/app/main.s"
	@echo "... src/console/ConfigDialog.o"
	@echo "... src/console/ConfigDialog.i"
	@echo "... src/console/ConfigDialog.s"
	@echo "... src/console/Console.o"
	@echo "... src/console/Console.i"
	@echo "... src/console/Console.s"
	@echo "... src/console/DefaultLayout.o"
	@echo "... src/console/DefaultLayout.i"
	@echo "... src/console/DefaultLayout.s"
	@echo "... src/module/EmptyPanel.o"
	@echo "... src/module/EmptyPanel.i"
	@echo "... src/module/EmptyPanel.s"
	@echo "... src/module/TablePanel.o"
	@echo "... src/module/TablePanel.i"
	@echo "... src/module/TablePanel.s"
	@echo "... src/util/CrossPlatformWebView.o"
	@echo "... src/util/CrossPlatformWebView.i"
	@echo "... src/util/CrossPlatformWebView.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

